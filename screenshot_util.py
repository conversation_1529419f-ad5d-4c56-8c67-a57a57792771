import os
import io
import base64
import logging
import asyncio
from datetime import datetime
import time
import json
import httpx
from PIL import Image, ImageGrab
from llm_response.config import GEMMA_CPP_MODEL_PATH, LLAMA_CPP_N_CTX, LLAMA_CPP_N_THREADS, LLAMA_CPP_USE_MMAP, LLAMA_CPP_USE_MLOCK
from llama_cpp import <PERSON>lama
from llm_response.llama_cpp_adapter import extract_response_from_llama_cpp_result
import threading

logger = logging.getLogger(__name__)

# Global cached model instance to prevent concurrent access violations
_screenshot_model_client = None
_screenshot_model_lock = threading.Lock()
_screenshot_inference_lock = asyncio.Lock()  # Async lock for inference

def get_screenshot_model_client():
    """Initialize and return a cached llama.cpp client for screenshot analysis."""
    global _screenshot_model_client
    if _screenshot_model_client is None:
        with _screenshot_model_lock:
            if _screenshot_model_client is None:
                try:
                    # Ensure model path exists
                    if not os.path.exists(GEMMA_CPP_MODEL_PATH):
                        logger.error(f"Screenshot model not found at: {GEMMA_CPP_MODEL_PATH}")
                        raise FileNotFoundError(f"Screenshot model not found at: {GEMMA_CPP_MODEL_PATH}")
                    
                    # Enable Vulkan offload
                    os.environ.setdefault("GGML_VULKAN", "1")
                    
                    # Create and cache the client
                    _screenshot_model_client = Llama(
                        model_path=GEMMA_CPP_MODEL_PATH,
                        n_ctx=LLAMA_CPP_N_CTX,
                        n_gpu_layers=-1,
                        n_threads=LLAMA_CPP_N_THREADS,
                        use_mmap=LLAMA_CPP_USE_MMAP,
                        use_mlock=LLAMA_CPP_USE_MLOCK,
                        verbose=False
                    )
                    logger.info("✅ Screenshot analysis llama.cpp client initialized successfully!")
                except Exception as e:
                    logger.error(f"❌ Failed to initialize screenshot model client: {e}")
                    raise
    return _screenshot_model_client

class OptimizedScreenshotManager:
    """Ultra-fast screenshot system optimized for LM Studio vision analysis."""
    
    def __init__(self):
        os.makedirs('screenshots', exist_ok=True)
        self.lm_studio_url = "http://127.0.0.1:1234/v1/chat/completions"
        self.api_key = "lm-studio-key"
        self.model_name = "gemma-3-4b-it-qat"
        
        # Load Luna's personality for vision tasks
        self.system_prompt = self._load_system_prompt()
        logger.info("Optimized Screenshot Manager initialized with Luna's personality")
    
    def _load_system_prompt(self):
        """Load Luna's system prompt for vision tasks."""
        try:
            # Try vision-specific prompt first
            if os.path.exists("system_prompt_vision.txt"):
                with open("system_prompt_vision.txt", "r", encoding="utf-8") as f:
                    prompt = f.read().strip()
                    logger.info("Loaded vision-specific system prompt")
                    return prompt
            # Fallback to general system prompt
            elif os.path.exists("system_prompt.txt"):
                with open("system_prompt.txt", "r", encoding="utf-8") as f:
                    prompt = f.read().strip()
                    logger.info("Loaded general system prompt for vision")
                    return prompt
            else:
                logger.warning("No system prompt file found, using default")
                return "You are Luna, a sassy AI assistant. Respond to vision tasks in a witty, casual way."
        except Exception as e:
            logger.error(f"Error loading system prompt: {e}")
            return "You are Luna, a sassy AI assistant. Respond to vision tasks in a witty, casual way."
        
    async def capture_and_analyze_fast(self, custom_prompt: str = None) -> dict:
        """
        Ultra-fast screenshot capture and analysis using base64 + LM Studio.
        This is the ONLY method we use - no fallbacks, no failed attempts.
        """
        start_time = time.monotonic()
        
        try:
            # Step 1: Fast screenshot capture
            capture_start = time.monotonic()
            screenshot = await asyncio.to_thread(ImageGrab.grab)
            capture_time = time.monotonic() - capture_start
            logger.info(f"Screenshot capture: {capture_time:.3f}s")
            
            # Step 2: Optimize image for fast analysis (use 1024x576 - our optimal size)
            process_start = time.monotonic()
            
            # Resize to optimal dimensions (1024 width, maintain aspect ratio)
            screenshot.thumbnail((1024, 1024), Image.LANCZOS)
            
            # Convert to RGB and compress
            if screenshot.mode != 'RGB':
                screenshot = screenshot.convert('RGB')
            
            output_buffer = io.BytesIO()
            screenshot.save(output_buffer, format='JPEG', quality=85, optimize=True)
            image_bytes = output_buffer.getvalue()
            
            process_time = time.monotonic() - process_start
            logger.info(f"Image processing: {process_time:.3f}s, size: {len(image_bytes)} bytes, dimensions: {screenshot.size}")
            
            # Step 3: Fast LM Studio API call (base64 only - we know this works)
            api_start = time.monotonic()
            analysis = await self._fast_lm_studio_call(image_bytes, custom_prompt)
            api_time = time.monotonic() - api_start
            
            total_time = time.monotonic() - start_time
            logger.info(f"TOTAL SCREENSHOT ANALYSIS: {total_time:.3f}s (capture: {capture_time:.3f}s, process: {process_time:.3f}s, api: {api_time:.3f}s)")
            
            # Save screenshot for reference
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"screenshots/screenshot_{timestamp}.jpg"
            with open(filename, 'wb') as f:
                f.write(image_bytes)
            
            return {
                "success": True,
                "analysis": analysis,
                "total_time": total_time,
                "image_size": len(image_bytes),
                "filename": filename,
                "bytes": image_bytes  # For compatibility
            }
            
        except Exception as e:
            logger.error(f"Screenshot analysis failed: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "analysis": f"Screenshot failed: {e}"
            }
    
    async def _fast_lm_studio_call(self, image_bytes: bytes, custom_prompt: str = None) -> str:
        """Fast screenshot analysis using cached llama.cpp client to prevent access violations."""
        # Prepare prompt: system prompt + optional text
        prompt_text = custom_prompt or (
            "I'm looking at Gavin's screen right now. Describe what you see in 2-3 sentences."
        )

        # Combine system and user prompt
        full_prompt = f"{self.system_prompt}\n\n{prompt_text}"

        try:
            # Use cached model client instead of creating new instance
            model = get_screenshot_model_client()

            # Use async lock to prevent concurrent access to the same model instance
            async with _screenshot_inference_lock:
                # Run inference in thread pool
                loop = asyncio.get_event_loop()
                def run_inference():
                    # llama.cpp doesn't ingest raw image here; we rely on model's vision integration if available
                    return model(full_prompt, max_tokens=150, temperature=0.8)

                result = await loop.run_in_executor(None, run_inference)
                # Extract analysis text
                analysis = extract_response_from_llama_cpp_result(result)
                return analysis
        except Exception as e:
            logger.error(f"Screenshot model inference failed: {e}")
            return f"Screenshot analysis failed: {e}"
    
    def get_mime_type(self, filename):
        """Simple MIME type detection for compatibility."""
        if filename.lower().endswith(('.jpg', '.jpeg')):
            return 'image/jpeg'
        elif filename.lower().endswith('.png'):
            return 'image/png'
        elif filename.lower().endswith('.gif'):
            return 'image/gif'
        else:
            return 'image/jpeg'  # Default
    
    async def analyze_image_url(self, image_url: str, prompt: str = None) -> str:
        """
        Analyze an image from URL using the same fast method.
        """
        try:
            # Download image
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(image_url)
                response.raise_for_status()
                image_bytes = response.content
            
            # Process image same way as screenshot
            image = Image.open(io.BytesIO(image_bytes))
            
            # Optimize same as screenshot
            image.thumbnail((1024, 1024), Image.LANCZOS)
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            output_buffer = io.BytesIO()
            image.save(output_buffer, format='JPEG', quality=85, optimize=True)
            optimized_bytes = output_buffer.getvalue()
            
            # Analyze using our fast method with personality
            analysis = await self._fast_lm_studio_call(optimized_bytes, prompt)
            return analysis
            
        except Exception as e:
            logger.error(f"Image URL analysis failed: {e}")
            return f"Could not analyze image: {e}"

# Create global instances
optimized_screenshot_manager = OptimizedScreenshotManager()
screenshot_manager = optimized_screenshot_manager  # Backward compatibility

# Legacy compatibility functions
async def capture_screenshot():
    """Legacy compatibility - redirects to optimized system"""
    result = await optimized_screenshot_manager.capture_and_analyze_fast()
    return result

async def analyze_image_url_fast(image_url: str, prompt: str = None) -> str:
    """Fast image URL analysis function."""
    return await optimized_screenshot_manager.analyze_image_url(image_url, prompt) 