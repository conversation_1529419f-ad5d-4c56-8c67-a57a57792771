import discord
import asyncio
import io
import time
import logging
import sqlite3
import re
import threading  # Add threading import for thread ID logging
from collections import defaultdict, deque
from speech_to_text import transcribe_audio
# Import necessary components from llm_response
import llm_response # Import the module to access model/functions
# Import the latency logging helper
from llm_response.processing import log_latency_to_discord
from llm_brain import get_brain_decision # Keep for now if used elsewhere, but add main processor
import llm_brain # Import the module itself to call the new processor function
from llm_response.config import get_main_name_from_alias, get_main_name_by_id, get_id_from_alias, is_minecraft_mode_enabled, GROUP_SILENCE_THRESHOLD, FAST_SESSION_LIMIT # Import alias helpers and silence threshold
# Import latency tracking
from llm_response.latency_tracker import start_latency_tracking, mark_latency_timestamp, set_response_length, complete_latency_tracking
import os

# Define the transcript log channel ID
TRANSCRIPT_LOG_CHANNEL_ID = 1369158552996024370

logger = logging.getLogger(__name__)

# Import the global mc_bridge from main.py
try:
    from main import mc_bridge
except ImportError:
    mc_bridge = None

class DiscordSink(discord.sinks.Sink):
    def __init__(self, text_channel, bot, voice_client, lm_studio_client, mc_bridge=None):
        super().__init__()
        self.text_channel = text_channel
        self.bot = bot
        self.voice_client = voice_client # Store the voice client instance
        self.user_audio = {}
        self.last_packet_time = {}
        self.audio_start_time = {}  # DEPRECATED - Use audio_detected_monotonic_time
        self.audio_detected_monotonic_time = {} # Track monotonic time when audio first detected for a user
        self.play_lock = asyncio.Lock()
        self.buffer = io.BytesIO()
        self.bg_task = None
        self.conversation_history = [] # Overall persistent history
        self.max_history_length = 150
        self.last_history_timestamp = time.time()
        self.system_prompt = ""
        self.is_speaking = False
        self.pending_transcriptions = {} # Tracks audio being actively transcribed
        self.last_transcription_time = 0
        self.transcription_cooldown = 0.05 # Cooldown between transcriptions for a user
        self.current_turn_start_monotonic_time = None  # Track turn start time for latency measurement
        self.is_processing_screenshot = False
        self.last_screenshot_time = 0
        self.screenshot_cooldown = 5 # Cooldown for screenshot command
        self.participation_counter = defaultdict(int) # Track user participation
        self.lm_studio_client = lm_studio_client # Store the passed LM Studio client
        self.chat_session = None # Initialize chat session attribute

        # --- Per-Transcription Processing State ---
        # Group silence variables removed - now processing each transcription individually

        # Store the DB path instead of the connection
        self.db_path = "conversation_logs.db"

        # --- Database Initialization (Main Thread) ---
        # Connect briefly just to initialize/check schema
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.execute("PRAGMA table_info(interactions)")
            existing_columns = {row[1] for row in cursor.fetchall()}
            required_columns = {
                'user_id', 'role', 'content', 'timestamp',
                'audio_start_time', 'channel_type', 'channel_id'
            }

            # Add missing columns
            for column in required_columns - existing_columns:
                if column == 'audio_start_time':
                    conn.execute("ALTER TABLE interactions ADD COLUMN audio_start_time REAL")
                elif column == 'channel_type':
                    conn.execute("ALTER TABLE interactions ADD COLUMN channel_type TEXT")
                elif column == 'channel_id':
                    conn.execute("ALTER TABLE interactions ADD COLUMN channel_id TEXT")

            # Create the table if it doesn't exist
            conn.execute('''CREATE TABLE IF NOT EXISTS interactions (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            user_id TEXT,
                            role TEXT,
                            content TEXT,
                            timestamp REAL,
                            audio_start_time REAL,
                            channel_type TEXT,
                            channel_id TEXT)''')
            conn.commit()
            conn.close()  # Close the connection immediately
            logger.info("Database schema checked/initialized.")
        except Exception as e:
            logger.error(f"Database initialization error: {e}")

        # Load conversation history (once at startup)
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.execute("SELECT role, content, timestamp, user_id FROM interactions ORDER BY timestamp ASC")
            rows = cursor.fetchall()
            self.conversation_history = []
            for row in rows:
                # Safely access row elements with bounds checking
                if len(row) >= 4:
                    self.conversation_history.append({
                        "role": row[0], 
                        "content": row[1], 
                        "timestamp": row[2], 
                        "user_id": row[3]
                    })
                else:
                    logger.warning(f"Skipping malformed database row with {len(row)} columns: {row}")
            conn.close()
            logger.info("Loaded %d historical interactions.", len(self.conversation_history))
        except Exception as e:
            logger.error("Error loading conversation history: %s", e)
            self.conversation_history = []  # Ensure it's an empty list on error

        # The system_prompt is now set by the caller (main.py) after initialization
        # try:
        #     with open('system_prompt.txt', 'r') as f:
        #         self.system_prompt = f.read()
        # except Exception as e:
        #     logger.error(f"Error loading system prompt: {e}")
        #     self.system_prompt = ""

        # --- Initialize Ollama Chat Session ---
        if self.lm_studio_client:
            try:
                logger.info("Starting Ollama Chat Session...")
                # Initialize session with system prompt
                session_id = f"voice_channel_{int(time.time())}"

                # Create a session object with the system prompt
                self.chat_session = {
                    "id": session_id,
                    "messages": []
                }

                # No system prompt for Ollama - using custom modelfile instead
                logger.info(f"Voice chat session initialized without system prompt (using Ollama custom modelfile). Session has {len(self.chat_session['messages'])} messages.")

                # Store the session in the bot's chat_sessions dictionary for global access
                voice_session_key = f"voice_{self.voice_client.channel.id}" if self.voice_client and self.voice_client.channel else "voice_unknown"
                self.bot.chat_sessions[voice_session_key] = self.chat_session
                logger.info(f"Stored voice chat session in bot.chat_sessions with key '{voice_session_key}'")

                logger.info("Ollama Chat Session initialized successfully.")
            except Exception as e:
                logger.error(f"Failed to initialize Ollama Chat Session: {e}", exc_info=True)
                self.chat_session = None # Ensure it's None on failure
        else:
            logger.error("LM Studio client not provided to DiscordSink, cannot initialize chat session.")

        # Mention processing flag removed - no longer needed with per-transcription processing

        # Add a class-level or instance-level mc_event_history
        self.mc_event_history = deque(maxlen=20)

        self.mc_bridge = mc_bridge

        # Random response timer
        self.random_response_timer = None
        self.random_response_active = False
        self._schedule_next_random_response()

    def write(self, data, user_id):
        """This method is called by discord.py when audio data is received."""
        current_monotonic = time.monotonic() # Use monotonic time for activity tracking
        current_time = time.time() # Use wall time for audio start timestamp

        # Initialize user audio buffer and start time if first packet
        if user_id not in self.user_audio:
            self.user_audio[user_id] = bytearray()
            # self.audio_start_time[user_id] = current_time # Store start time (DEPRECATED)
            self.audio_detected_monotonic_time[user_id] = current_monotonic # Store monotonic time
            logger.debug(f"New speaker {user_id} detected at monotonic time {current_monotonic:.4f}")
            # REMOVED: Cannot call asyncio.create_task from this synchronous thread context

        # Append data and update timestamps
        self.user_audio[user_id].extend(data)
        self.last_packet_time[user_id] = current_monotonic # Update user's last packet time using monotonic
        # Activity time tracking removed - no longer needed with per-transcription processing

    async def _stream_to_buffer(self, response):
        try:
            for chunk in response.iter_bytes():
                self.buffer.write(chunk)
        except Exception as e:
            logger.error(f"Error in stream_to_buffer: {e}")

    def _run_db_write_sync(self, db_path, query, params):
        """Synchronous function to run DB writes with its own connection."""
        conn = None  # Initialize conn
        try:
            # Create a new connection specifically for this thread
            conn = sqlite3.connect(db_path, timeout=10)  # Added timeout
            cursor = conn.cursor()
            cursor.execute(query, params)
            conn.commit()
            logger.debug(f"Successfully wrote to DB in thread {threading.get_ident()}")
            return True
        except sqlite3.OperationalError as e:
            logger.warning(f"Database insert error: {e}")
            # Fallback logic (ensure conn exists)
            try:
                if conn:
                    fallback_query = "INSERT INTO interactions (user_id, role, content, timestamp) VALUES (?, ?, ?, ?)"
                    fallback_params = (params[0], params[1], params[2], params[3])
                    cursor = conn.cursor()
                    cursor.execute(fallback_query, fallback_params)
                    conn.commit()
                    logger.debug(f"Successfully wrote fallback to DB in thread {threading.get_ident()}")
                    return True
                else:
                    logger.error("Cannot perform fallback DB write without connection.")
                    return False
            except Exception as e2:
                logger.error(f"Fallback database insert error: {e2}")
                return False
        except Exception as e_gen:
            logger.error(f"General database write error: {e_gen}")
            return False
        finally:
            # Ensure the connection is closed if it was opened
            if conn:
                conn.close()
                logger.debug(f"Closed DB connection in thread {threading.get_ident()}")

    async def _add_to_history(self, role, content, user_id="unknown", audio_timestamp=None, channel_type="voice", channel_id=None):
        """Add message to conversation history with asynchronous database operations."""
        now = time.time()
        segmentation_threshold = 300
        if now - self.last_history_timestamp > segmentation_threshold:
            self.conversation_history = []
        self.last_history_timestamp = now

        # Use the audio start timestamp if provided, otherwise use current time
        timestamp = audio_timestamp or now

        # Create the message entry with timestamp and channel info
        message_entry = {
            "role": role,
            "content": content,
            "timestamp": timestamp,
            "user_id": user_id,
            "channel_type": channel_type,
            "channel_id": channel_id
        }

        # --- Update in-memory conversation history (fast operation) ---
        if len(self.conversation_history) > 0 and timestamp < self.conversation_history[-1]["timestamp"]:
            # Find the right position to insert it
            insert_pos = len(self.conversation_history)
            for i, msg in enumerate(reversed(self.conversation_history)):
                if timestamp >= msg["timestamp"]:
                    insert_pos = len(self.conversation_history) - i
                    break
                if i == len(self.conversation_history) - 1:
                    insert_pos = 0

            self.conversation_history.insert(insert_pos, message_entry)
            logger.info(f"Inserted message from {user_id} at position {insert_pos} (out of time order)")
        else:
            # Normal case - append to history
            self.conversation_history.append(message_entry)

        # Trim if too long
        if len(self.conversation_history) > self.max_history_length:
            self.conversation_history.pop(0)

        # --- Perform database write asynchronously ---
        db_query = """
            INSERT INTO interactions
            (user_id, role, content, timestamp, audio_start_time, channel_type, channel_id)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """
        db_params = (user_id, role, content, now, timestamp, channel_type, channel_id)

        # Run the synchronous DB write function in a separate thread
        await asyncio.to_thread(
            self._run_db_write_sync,
            self.db_path,  # Pass the path instead of the connection
            db_query,
            db_params
        )

    def _after_play(self, error, user_id): # user_id here might be the *last* speaker, not necessarily the one who triggered the response
        playback_end_time = time.monotonic()
        self.is_speaking = False
        # Log total duration if start time is available
        if self.current_turn_start_monotonic_time:
            total_duration = playback_end_time - self.current_turn_start_monotonic_time
            # Use create_task as this isn't an async function
            asyncio.create_task(log_latency_to_discord(self.bot, f"Total Turn Processing -> Playback End: {total_duration:.4f}s"))
            self.current_turn_start_monotonic_time = None # Reset for next turn
        if error:
            logger.error(f"Playback error: {error}")
        if user_id in self.user_audio:
            self.user_audio[user_id].clear()
        if user_id in self.last_packet_time:
            del self.last_packet_time[user_id]
        if user_id in self.audio_start_time:
            del self.audio_start_time[user_id]

    def _quick_trigger_check(self, text):
        text_lower = text.lower()
        direct_triggers = {'luna', 'bot', 'assistant', 'ai', 'hey you', 'robot', '@luna'}
        if any(trigger in text_lower for trigger in direct_triggers):
            return True
        if '?' in text:
            return True
        cues = {'what do you think', 'opinion', 'suggest', 'idea', 'what about'}
        if any(cue in text_lower for cue in cues):
            return True
        mention_patterns = {
            r'\byou\b.*\bthink\b',
            r'\bhow about\b.*\byou\b',
            r'\bwhat\'?s your\b'
        }
        for pattern in mention_patterns:
            if re.search(pattern, text_lower):
                return True
        return False

    async def check_for_silence(self):
        while True:
            await asyncio.sleep(0.05) # Reduced interval from 0.1 for faster checking
            current_time = time.time() # Use wall time for silence check comparison

            for user_id, audio_bytes in list(self.user_audio.items()):
                if audio_bytes:
                    # Use monotonic time for time_since_last calculation
                    last_packet_mono = self.last_packet_time.get(user_id, time.monotonic())
                    time_since_last = time.monotonic() - last_packet_mono

                    # Reduce silence threshold to trigger transcription faster
                    if time_since_last > 0.4: # Reduced from 0.8
                        # Check transcription cooldown using wall time
                        if time.time() - self.last_transcription_time < self.transcription_cooldown:
                            continue

                        # Get the audio start time for proper ordering (wall time)
                        audio_start_time = self.audio_start_time.get(user_id, current_time)

                        # --- Start preparing transcription task ---
                        # Moved user fetching inside _process_transcription to avoid blocking this loop
                        try:
                            self.last_transcription_time = time.time() # Update cooldown timer

                            # Add minimal info needed to pending transcriptions
                            self.pending_transcriptions[user_id] = {
                                "start_time": audio_start_time, # Wall time
                                "audio_bytes": bytes(audio_bytes), # Create bytes copy
                                "audio_detected_time": self.audio_detected_monotonic_time.get(user_id) # Monotonic time
                            }

                            # Start the transcription asynchronously
                            pre_create_task_time = time.monotonic()
                            logger.info(f"DIAG: Creating task _process_transcription for user {user_id} at {pre_create_task_time:.4f}")
                            asyncio.create_task(self._process_transcription(user_id))

                        except Exception as task_prep_e:
                             # Catch errors specifically related to preparing/starting the task
                             logger.error(f"Error preparing transcription task for {user_id}: {task_prep_e}", exc_info=True)
                        finally:
                             # Always clear audio buffer *after* attempting to create the task
                             if user_id in self.user_audio:
                                 self.user_audio[user_id].clear()
                             if user_id in self.last_packet_time:
                                 del self.last_packet_time[user_id]
                             # Clean up monotonic time tracker as well
                             if user_id in self.audio_detected_monotonic_time:
                                 del self.audio_detected_monotonic_time[user_id]
                        # --- End preparing transcription task ---

                        # Removed the except blocks for fetch errors and the general 'Error preparing task'
                        # as the fetch logic is moved and the task prep error is handled above.
                        # The finally block for clearing audio is now inside the inner try/except/finally.
            # End of per-user silence check loop

            # --- Group Silence Detection Disabled ---
            # Per-transcription decision system is now active
            # Each transcription is processed individually with full context
            # Group silence logic has been removed in favor of immediate per-transcription processing

    async def _process_transcription(self, user_id):
        """Transcribes audio for a user and adds it to the current turn's transcripts."""
        process_start_time = time.monotonic() # DIAGNOSTIC TIMING
        logger.info(f"DIAG: _process_transcription called for user {user_id} at {process_start_time:.4f}") # DIAGNOSTIC TIMING

        # Ensure we still have pending data (might have been cleared by a rapid stop/start)
        if user_id not in self.pending_transcriptions:
            logger.warning(f"_process_transcription called for {user_id} but no pending data found.")
            return

        pending = self.pending_transcriptions[user_id]
        audio_bytes = pending["audio_bytes"]
        audio_start_time = pending["start_time"] # Wall time
        audio_detected_time = pending.get("audio_detected_time") # Monotonic time

        # --- Fetch user info and determine names (Moved here) ---
        display_name = f"User_{user_id}" # Default fallback
        # First try to get the canonical name directly from the user ID
        canonical_name = get_main_name_by_id(user_id)

        # If we couldn't get a canonical name from the ID, try to fetch the user and resolve from display name
        if not canonical_name:
            try:
                user = await self.bot.fetch_user(user_id)
                if self.text_channel and hasattr(self.text_channel, 'guild'):
                    guild = self.text_channel.guild
                    try:
                        member = await guild.fetch_member(user_id)
                        if member:
                            display_name = member.nick or member.name # Use nick first, then name
                            canonical_name = get_main_name_from_alias(display_name) # Resolve potential alias
                    except discord.NotFound:
                        logger.debug(f"Member {user_id} not found in guild {guild.id}, using fetched user name.")
                        display_name = user.name
                        canonical_name = get_main_name_from_alias(display_name) # Resolve potential alias
                    except discord.Forbidden:
                        logger.warning(f"Lacking permissions to fetch member {user_id} in guild {guild.id}.")
                        display_name = user.name # Fallback to fetched user name
                        canonical_name = get_main_name_from_alias(display_name)
                    except Exception as fetch_mem_e:
                        logger.error(f"Error fetching member {user_id}: {fetch_mem_e}")
                        display_name = user.name # Fallback to fetched user name
                        canonical_name = get_main_name_from_alias(display_name)
                else:
                    logger.debug(f"No guild context available, using fetched user name for {user_id}.")
                    display_name = user.name
                    canonical_name = get_main_name_from_alias(display_name) # Resolve potential alias

            except discord.NotFound:
                logger.warning(f"Could not fetch user {user_id} globally (NotFound). Using fallback name '{display_name}'.")
                # Keep fallback names assigned above
            except Exception as fetch_user_e:
                logger.error(f"Error fetching user {user_id}: {fetch_user_e}", exc_info=True)
                # Keep fallback names assigned above

        # If we still don't have a canonical name, use the display name
        if not canonical_name:
            canonical_name = display_name

        # --- Proceed with Transcription ---
        try:
            # Build the Minecraft context string ONLY if enabled
            mc_context_lines = []
            if is_minecraft_mode_enabled() and self.mc_bridge is not None:
                for event_type, data in list(self.mc_event_history)[-10:]:
                    if event_type == "public_chat":
                        mc_context_lines.append(f"[MC Chat] {data.get('message')}")
                    elif event_type == "action_start":
                        mc_context_lines.append(f"[MC Action Start] {data.get('action')}")
                    elif event_type == "action_end":
                        mc_context_lines.append(f"[MC Action End] {data.get('action')} ({data.get('status')})")
                    elif event_type == "thought":
                        mc_context_lines.append(f"[MC Thought] {data.get('thought')}")
            mc_context = '\n'.join(mc_context_lines)
            if mc_context:
                prompt_with_mc = (
                    "You are Luna, and the following is a summary of your recent actions, thoughts, and chat in Minecraft. "
                    "Use this to inform your response. These are YOUR actions, not the user's.\n"
                    "[Your Recent Minecraft Activity]\n"
                    f"{mc_context}\n\n"
                    f"{self.system_prompt}"
                )
            else:
                prompt_with_mc = self.system_prompt

            # Transcribe the audio using the determined display_name for logging
            logger.info(f"Starting transcription for {display_name}")
            pre_transcribe_call_time = time.monotonic() # DIAGNOSTIC TIMING
            logger.info(f"DIAG: Calling transcribe_audio at {pre_transcribe_call_time:.4f}") # DIAGNOSTIC TIMING
            transcribe_start_time = time.monotonic()
            text = await transcribe_audio(audio_bytes, user_id=user_id)  # Pass user_id for volume adjustment
            transcription_end_time = time.monotonic() # Capture end time
            transcription_duration = transcription_end_time - transcribe_start_time
            await log_latency_to_discord(self.bot, f"Transcription Duration ({canonical_name}): {transcription_duration:.4f}s") # Log with canonical name

            if not text:
                logger.info(f"Transcription for {canonical_name} resulted in empty text.") # Log with canonical name
                # Still update activity time even if transcription is empty, as processing happened
                self.last_activity_time = time.monotonic()
                # Clean up handled in finally block now
                return

            logger.info(f"Transcription for {canonical_name} (started at {audio_start_time:.2f}): {text}") # Log with canonical name

            # --- Per-Transcription Processing ---
            luna_mentioned = "luna" in text.lower()
            # No longer adding to current_turn_transcripts since we process each transcription individually

            # --- Forward voice command to Minecraft if Luna is mentioned ---
            if is_minecraft_mode_enabled() and luna_mentioned and self.mc_bridge is not None:
                import re
                cleaned = re.sub(r"(?i)luna[,:;\\-\\s]*", "", text, count=1).strip()
                self.mc_bridge.send_command(cleaned)
                logger.info(f"Forwarded to Minecraft (voice): {cleaned}")

            # --- Per-Transcription Decision System ---
            # Process each transcription individually with full context
            logger.info(f"Processing transcription from {canonical_name} with per-transcription decision system")
            
            # Add this transcript to history first
            await self._add_to_history(
                role="user",
                content=f"{canonical_name}: {text}",
                user_id=user_id,
                audio_timestamp=audio_start_time,
                channel_type="voice",
                channel_id=str(self.voice_client.channel.id) if self.voice_client and self.voice_client.channel else None
            )
            
            # Start latency tracking for this individual transcription
            from llm_response.latency_tracker import start_latency_tracking, mark_latency_timestamp
            turn_id = start_latency_tracking(
                user_id=str(user_id),
                user_name=canonical_name
            )
            
            # Mark transcription end time
            mark_latency_timestamp("transcription_end", transcription_end_time)
            
            # Check if chat_session exists and has messages
            if self.chat_session is None or not isinstance(self.chat_session, dict) or "messages" not in self.chat_session:
                logger.warning("Voice chat session is missing or invalid. Creating a new one.")
                # Create a new session
                session_id = f"voice_channel_{int(time.time())}"
                self.chat_session = {
                    "id": session_id,
                    "messages": []
                }
                logger.info(f"New voice chat session initialized. Session has {len(self.chat_session['messages'])} messages.")
            
            # Enhanced per-transcription processing with full context
            try:
                # Check if automatic screenshot analysis should be included
                image_analysis_text = None
                screenshot_keywords = ["screen", "screenshot", "see", "look", "show", "what", "display", "monitor", "computer"]
                
                # Include screenshot analysis if Luna is mentioned and text suggests visual context
                if luna_mentioned and any(keyword in text.lower() for keyword in screenshot_keywords):
                    try:
                        logger.info(f"Luna mentioned with visual context by {canonical_name}, including screenshot analysis")
                        from screenshot_util import optimized_screenshot_manager
                        screenshot_result = await optimized_screenshot_manager.capture_and_analyze_fast(
                            f"User {canonical_name} said: '{text}'. Analyze what's on screen in context of this request."
                        )
                        if screenshot_result["success"]:
                            image_analysis_text = screenshot_result["analysis"]
                            logger.info(f"Screenshot analysis included: {image_analysis_text[:100]}...")
                    except Exception as screenshot_err:
                        logger.error(f"Screenshot analysis failed for {canonical_name}: {screenshot_err}")
                
                # Get recent background observations for additional context
                try:
                    from llm_response.auto_screenshot import auto_screenshot_manager
                    recent_observations = auto_screenshot_manager.get_recent_observations(limit=2)
                    if recent_observations and not image_analysis_text:
                        # Add background context if we don't have fresh screenshot
                        latest_obs = recent_observations[-1]
                        obs_time_ago = int(time.time() - latest_obs.get("timestamp", 0))
                        if obs_time_ago < 120:  # Only if within last 2 minutes
                            image_analysis_text = f"Background context ({obs_time_ago}s ago): {latest_obs.get('content', '')}"
                            logger.info(f"Added background screenshot context from {obs_time_ago}s ago")
                except Exception as bg_err:
                    logger.debug(f"Could not get background observations: {bg_err}")

                import llm_response.processing
                await llm_response.processing.process_user_message(
                    bot=self.bot,
                    text=text,
                    user_id=user_id,
                    conversation_history=self.conversation_history,
                    system_prompt=self.system_prompt,
                    sink=self,
                    force_respond=luna_mentioned,  # Force respond only if Luna was mentioned
                    display_name=canonical_name,
                    text_channel=self.text_channel,
                    image_analysis_text=image_analysis_text,  # Enhanced with screenshot analysis
                    chat_session=self.chat_session,
                    # Pass latency timestamps
                    audio_detected_time=audio_detected_time,
                    transcription_end_time=transcription_end_time,
                    mc_bridge=self.mc_bridge
                )
                logger.info(f"Successfully processed individual transcription from {canonical_name} with full context")
            except Exception as e:
                logger.error(f"Error processing individual transcription from {canonical_name}: {e}", exc_info=True)

            # --- Log transcription to the dedicated channel ---
            try:
                log_channel = self.bot.get_channel(TRANSCRIPT_LOG_CHANNEL_ID)
                if log_channel:
                    log_message = f"**{canonical_name}:** {text}" # Log with canonical name
                    # Use create_task to avoid blocking transcription processing
                    asyncio.create_task(log_channel.send(log_message))
                else:
                    logger.warning(f"Transcript log channel {TRANSCRIPT_LOG_CHANNEL_ID} not found.")
            except Exception as log_err:
                logger.error(f"Error sending transcription to log channel: {log_err}", exc_info=True)

        # End of try block
        except Exception as e: # Ensure this aligns with try
            logger.error(f"Error during transcription processing for {user_id}: {e}", exc_info=True)
        finally: # Ensure this aligns with try
            # Clean up this specific pending transcription regardless of outcome
            # Ensure key exists before deleting, although initial check should cover this
            if user_id in self.pending_transcriptions:
                del self.pending_transcriptions[user_id]

    # Note: _respond_to_luna is likely no longer needed with the new logic,
    # but keep it for now in case handle_screenshot_command still uses it indirectly.
    # We might need to refactor screenshot handling later.
    async def _respond_to_luna(self, text, display_name, user_id):
        # This function might need refactoring or removal later
        logger.warning(f"_respond_to_luna called directly for {display_name} - This might be deprecated logic.")
        try:
            # Call process_user_message using keyword arguments
            # This likely needs adjustment for turn-based context
            await llm_response.process_user_message( # Add module prefix
                bot=self.bot,
                text=text, # This only passes the single utterance, not the turn context
                user_id=user_id,
                conversation_history=self.conversation_history, # Uses overall history, not turn context
                system_prompt=self.system_prompt,
                sink=self,
                force_respond=True,
                display_name=display_name,
                text_channel=self.text_channel
            )
        except Exception as e:
            logger.error(f"Error in _respond_to_luna: {e}", exc_info=True)
            if not self.is_speaking and self.bot.voice_clients:
                await self.text_channel.send("Sorry, I'm having trouble responding right now.")

    async def process_text_message(self, message):
        # This function needs review - should text messages contribute to the voice turn?
        # For now, keep it separate. It calls process_user_message directly.
        try:
            # Skip messages from bots (except our own responses)
            if message.author.bot and message.author.id != self.bot.user.id:
                return

            # Extract content and metadata
            content = message.content
            author_id = str(message.author.id)
            author_name = message.author.display_name
            timestamp = message.created_at.timestamp()
            # Handle different channel types (DMChannel doesn't have a name attribute)
            channel_name = message.channel.name if hasattr(message.channel, 'name') else "DM"

            # Check for Luna mentions
            luna_mentioned = any([
                self.bot.user.mentioned_in(message),
                "luna" in content.lower(),
                re.search(r'@luna\b', content, re.IGNORECASE)
            ])

            # Add to conversation history (same format as voice)
            await self._add_to_history(
                "user",
                f"{author_name}: {content}",
                author_id,
                timestamp,
                channel_type="text",
                channel_id=str(message.channel.id)
            )

            # Rate limit responses in the channel to avoid overactivity
            current_time = time.time()
            recent_responses = [msg for msg in self.conversation_history[-10:]
                              if msg.get("role") == "assistant"
                              and msg.get("channel_id") == str(message.channel.id)
                              and current_time - msg.get("timestamp", 0) < 60]  # Last minute

            # If we've been very active in this channel and not directly mentioned, be more selective
            if len(recent_responses) >= 3 and not luna_mentioned:
                # Only respond to very clear triggers when we've been active
                if "?" not in content or not any(word in content.lower() for word in ["you", "your"]):
                    # Handle different channel types for logging
                    channel_display = message.channel.name if hasattr(message.channel, 'name') else "DM"
                    logger.info(f"Skipping response due to high activity in channel: {channel_display}")
                    return

            if luna_mentioned:
                # Call process_user_message directly for text messages mentioning Luna
                await llm_response.process_user_message( # Add module prefix
                    bot=self.bot,
                    text=content,
                    user_id=author_id,
                    conversation_history=self.conversation_history,
                    system_prompt=self.system_prompt,
                    sink=self,
                    force_respond=True, # Force response if mentioned
                    display_name=author_name,
                    text_channel=message.channel
                )
        except Exception as e:
            logger.error(f"Error processing text message: {e}", exc_info=True)

    async def _execute_dm_action(self, brain_action_details):
        """Execute a DM action for voice commands."""
        try:
            target_identifier = brain_action_details.get('target')
            exact_message = brain_action_details.get('exact_message')
            topic = brain_action_details.get('topic')

            if not target_identifier or (not exact_message and not topic):
                logger.error(f"Missing DM target or content: target='{target_identifier}', exact_message='{exact_message}', topic='{topic}'")
                return False

            if exact_message:
                logger.info(f"Executing DM action: target='{target_identifier}', exact_message='{exact_message}'")
            else:
                logger.info(f"Executing DM action: target='{target_identifier}', topic='{topic}'")

            # Import necessary functions
            from llm_response import get_ollama_client, log_message_to_db
            from llm_response.config import OLLAMA_MODEL_NAME, DM_TEMP, DM_MAX_OUTPUT_TOKENS
            from utils import find_user_from_identifier
            import asyncio
            import time

            # Get Ollama client
            ollama_client = get_ollama_client()
            logger.info(f"DEBUG: Ollama client for voice DM: {ollama_client}")

            if not ollama_client:
                logger.error("Cannot generate DM content: Ollama client not available.")
                if self.text_channel:
                    await self.text_channel.send("❌ Cannot generate DM content: Ollama client not available.")
                return False

            # Find the target user
            guild_context = self.voice_client.guild if self.voice_client else None
            target_user = await find_user_from_identifier(
                identifier=target_identifier,
                bot=self.bot,
                guild=guild_context
            )

            if not target_user:
                logger.warning(f"Could not find user matching '{target_identifier}' to send DM.")
                if self.text_channel:
                    await self.text_channel.send(f"❓ Brain wanted to DM '{target_identifier}', but I couldn't find them.")
                return False

            # Determine DM content - use exact message if provided, otherwise generate from topic
            dm_content = None
            if exact_message:
                # Use the exact message provided
                dm_content = exact_message
                logger.info(f"Using exact message for DM: '{dm_content}'")
            else:
                # Generate DM content using Ollama for topic-based requests
                logger.info(f"Generating DM content for target '{target_identifier}' about '{topic}'...")

                # Get or create DM chat session for statefulness
                dm_session_key = f"dm_{target_user.id}"
                dm_session = self.bot.chat_sessions.get(dm_session_key)

                if dm_session is None:
                    logger.info(f"Creating new DM chat session for user {target_user.id}")
                    dm_session = {
                        "id": f"dm_{target_user.id}_{int(time.time())}",
                        "messages": []  # No system prompt for Ollama - using custom modelfile instead
                    }
                    self.bot.chat_sessions[dm_session_key] = dm_session

                # Simplified prompt for DM generation
                simple_dm_prompt = f"You need to send a DM about: '{topic}'. Generate a short, casual message:"

                # Use the DM session messages and add the new prompt
                messages = dm_session["messages"].copy()
                messages.append({"role": "user", "content": simple_dm_prompt})

                # Make the API call using Ollama's fast generate endpoint
                try:
                    # Import the ultra-fast Ollama function from llm_response.processing
                    from llm_response.processing import _ultra_fast_ollama_non_streaming_call
                    
                    # Use the ultra-fast generate endpoint for DM generation
                    dm_content = await _ultra_fast_ollama_non_streaming_call(
                        messages=messages,
                        model=OLLAMA_MODEL_NAME,
                        temperature=DM_TEMP,
                        max_tokens=DM_MAX_OUTPUT_TOKENS,
                        bot=self.bot
                    )
                    
                except Exception as api_error:
                    logger.warning(f"LLM DM content generation failed: {api_error}")
                    if self.text_channel:
                        await self.text_channel.send(f"⚠️ Tried to DM '{target_identifier}' about '{topic}', but couldn't generate content (Error: {api_error}).")
                    return False

                dm_content = dm_content.strip() if dm_content else ""
                if not dm_content:
                    logger.warning(f"LLM generated empty DM content for topic: {topic}")
                    if self.text_channel:
                        await self.text_channel.send(f"⚠️ Tried to DM '{target_identifier}' about '{topic}', but couldn't think of what to say!")
                    return False

            # Send the DM
            try:
                # Update DM session with appropriate content
                if exact_message:
                    # For exact messages, log as direct message relay
                    dm_session_key = f"dm_{target_user.id}"
                    dm_session = self.bot.chat_sessions.get(dm_session_key)
                    if dm_session:
                        dm_session["messages"].append({"role": "user", "content": f"Relay message: {exact_message}"})
                        dm_session["messages"].append({"role": "assistant", "content": dm_content})
                else:
                    # For generated messages, use the existing session structure
                    dm_session["messages"].append({"role": "user", "content": simple_dm_prompt})
                    dm_session["messages"].append({"role": "assistant", "content": dm_content})

                await target_user.send(dm_content)
                logger.info(f"Successfully sent DM to {target_user.name} ({target_user.id})")

                # Log to database with appropriate description
                if exact_message:
                    log_description = f"Sent DM to {target_user.name} ({target_user.id}) with exact message: {dm_content}"
                else:
                    log_description = f"Sent DM to {target_user.name} ({target_user.id}) about '{topic}': {dm_content}"
                
                log_message_to_db(
                    user_id=str(self.bot.user.id), role="assistant_dm",
                    content=log_description,
                    timestamp=time.time(), channel_type="dm", channel_id=str(target_user.id)
                )

                return True

            except Exception as send_err:
                logger.error(f"Failed to send DM to {target_user.name}: {send_err}")
                if self.text_channel:
                    if exact_message:
                        await self.text_channel.send(f"❌ Failed to send your message to {target_user.mention}: {send_err}")
                    else:
                        await self.text_channel.send(f"❌ Failed to send DM to {target_user.mention}: {send_err}")
                return False

        except Exception as e:
            logger.error(f"Error executing DM action: {e}", exc_info=True)
            if self.text_channel:
                await self.text_channel.send(f"❌ An unexpected error occurred while trying to process the DM.")
            return False

    # Removed redundant _generate_and_send_text_response function.
    # Its logic is handled by llm_response.process_user_message.
    def get_recent_speaker_turns(self, count=5) -> list:
        """Gets the last 'count' unique speaker turns from history."""
        turns = []
        speakers_seen = set()
        # Iterate backwards through history
        for msg in reversed(self.conversation_history):
            user_id = msg.get("user_id")
            # Only consider user messages
            if msg.get("role") == "user" and user_id:
                if user_id not in speakers_seen:
                    # Get the canonical name for this user ID
                    # Convert user_id to int if it's a string, or use it directly if it's already an int
                    user_id_int = int(user_id) if isinstance(user_id, str) else user_id
                    canonical_name = get_main_name_by_id(user_id_int)

                    # If we couldn't get a canonical name from the ID, try to extract from the message content
                    if not canonical_name:
                        # Try to extract name from content (format is typically "Name: message")
                        content = msg.get("content", "")
                        name_match = re.match(r"^([^:]+):", content)
                        if name_match:
                            extracted_name = name_match.group(1).strip()
                            # Use the extracted name or resolve it to a canonical name
                            canonical_name = get_main_name_from_alias(extracted_name)

                    # If we still don't have a name, use a fallback
                    display_name = canonical_name or f"User_{user_id}"

                    # Extract the actual message text (remove the name prefix if present)
                    content = msg.get("content", "")
                    text = content
                    text_match = re.match(r"^[^:]+:\s*(.*)", content)
                    if text_match:
                        text = text_match.group(1)

                    turns.append({"user_id": user_id, "display_name": display_name, "text": text})
                    speakers_seen.add(user_id)
                    if len(turns) >= count:
                        break
        return list(reversed(turns)) # Return in chronological order

    async def _process_collected_turn(self, triggered_by_mention: dict | None = None, triggered_by_random: bool = False):
        """Processes the accumulated transcripts for the completed conversational turn."""
        # Wait for memory system to be ready before processing any voice turns
        if hasattr(self.bot, 'memory_ready'):
            try:
                await asyncio.wait_for(self.bot.memory_ready.wait(), timeout=30.0)
                logger.debug("Memory system ready, proceeding with voice turn processing")
            except asyncio.TimeoutError:
                logger.warning("Memory system initialization timed out after 30 seconds, proceeding anyway")
        else:
            logger.warning("Bot has no memory_ready attribute, proceeding without memory system check")
        
        # Use a lock to prevent multiple concurrent processing attempts for the same turn
        async with self.turn_processing_lock:
            # If triggered by mention, set flag to prevent group silence interference
            if triggered_by_mention:
                self.mention_processing_active = True
            try:
                # Double-check if transcripts are present (might have been processed by a rapid Luna mention)
                if not self.current_turn_transcripts and not triggered_by_mention and not triggered_by_random:
                    logger.debug("_process_collected_turn called but no transcripts found, likely already processed.")
                    return

                # --- Prepare Turn Data (Handles Mention Trigger vs Group Silence vs Random) ---
                transcripts_to_process = []
                luna_was_mentioned_in_turn = False
                is_mention_trigger = False # Flag to know which path was taken
                is_random_trigger = triggered_by_random

                if triggered_by_mention:
                    # --- Case 1: Triggered by a specific "Luna" mention ---
                    logger.info(f"Processing turn triggered by direct mention: {triggered_by_mention['original_display_name']}: {triggered_by_mention['text']}") # Use original_display_name
                    transcripts_to_process = [triggered_by_mention]
                    luna_was_mentioned_in_turn = True # Explicitly true
                    is_mention_trigger = True

                    # Clear the shared list immediately to discard other concurrent speech from this turn
                    self.current_turn_transcripts.clear()
                    logger.info("Cleared concurrent transcripts because turn was triggered by mention.")

                elif triggered_by_random:
                    # --- Case 2: Triggered by random timer ---
                    logger.info("Processing turn triggered by random response timer")
                    # For random responses, we'll use recent conversation history
                    # Get the last few speaker turns for context
                    recent_turns = self.get_recent_speaker_turns(count=3)
                    if recent_turns:
                        # Create pseudo-transcripts from recent turns for context
                        for turn in recent_turns:
                            pseudo_transcript = {
                                "user_id": turn['user_id'],
                                "original_display_name": turn['display_name'],
                                "canonical_name": turn['display_name'],
                                "text": turn['text'],
                                "timestamp": time.time(),  # Current time
                                "mentioned_luna": False,
                                "is_pseudo": True  # Mark as pseudo for random response
                            }
                            transcripts_to_process.append(pseudo_transcript)
                    
                    if not transcripts_to_process:
                        logger.info("No recent conversation for random response, creating generic context")
                        # Create a generic pseudo-transcript to allow random response
                        pseudo_transcript = {
                            "user_id": "random",
                            "original_display_name": "Random",
                            "canonical_name": "Random",
                            "text": "General conversation context",
                            "timestamp": time.time(),
                            "mentioned_luna": False,
                            "is_pseudo": True
                        }
                        transcripts_to_process.append(pseudo_transcript)

                else:
                    # --- Case 3: Triggered by group silence ---
                    logger.info("🔍 Group silence detected - analyzing conversation for potential response")
                    # Take a snapshot and clear the list *immediately* within the lock
                    turn_transcripts = list(self.current_turn_transcripts) # Make a copy
                    self.current_turn_transcripts.clear() # Clear the shared list

                    if not turn_transcripts:
                        logger.warning("Processing turn (group silence), but no transcripts found after lock.")
                        return # Nothing to process

                    # Sort transcripts by their original audio start time for correct order
                    turn_transcripts.sort(key=lambda x: x.get("timestamp", 0))
                    transcripts_to_process = turn_transcripts # Process all collected transcripts

                    # Log the conversation context that will be analyzed
                    conversation_summary = " | ".join([f"{t['canonical_name']}: {t['text'][:50]}..." for t in transcripts_to_process])
                    logger.info(f"📝 Conversation context for analysis: {conversation_summary}")

                    # Check if Luna was mentioned anywhere in this collected turn
                    luna_was_mentioned_in_turn = any(t.get("mentioned_luna", False) for t in transcripts_to_process)

                # Reset activity timer and capture turn processing start time
                self.current_turn_start_monotonic_time = time.monotonic() # Store start time for this processing
                self.last_activity_time = self.current_turn_start_monotonic_time # Reset activity timer
                
                # Start latency tracking for this turn
                trigger_type = "mention" if is_mention_trigger else "silence"
                display_name_for_tracking = transcripts_to_process[-1]['original_display_name'] if transcripts_to_process else "Unknown"
                user_id_for_tracking = transcripts_to_process[-1]['user_id'] if transcripts_to_process else 0
                
                # Get the transcription end time from the last utterance if available  
                transcription_end_time = None
                if transcripts_to_process:
                    transcription_end_time = transcripts_to_process[-1].get("transcription_end_time")
                
                turn_id = start_latency_tracking(
                    user_id=str(user_id_for_tracking),
                    user_name=display_name_for_tracking
                )
                
                # Mark transcription end time if we have it
                if transcription_end_time:
                    mark_latency_timestamp("transcription_end", transcription_end_time)

                # Combine text for logging
                combined_text_log = " | ".join([f"{t['original_display_name']}: {t['text']}" for t in transcripts_to_process]) # Use original_display_name
                logger.info(f"Processing turn data (mention_trigger={is_mention_trigger}, mentioned_in_turn={luna_was_mentioned_in_turn}, count={len(transcripts_to_process)}): {combined_text_log}")

                # --- Add Processed Transcripts to Overall History ---
                # Add each transcript from the processed turn (either the single mention or the group)
                for transcript in transcripts_to_process:
                    await self._add_to_history(
                        role="user",
                        content=f"{transcript['canonical_name']}: {transcript['text']}", # Use canonical name for history
                        user_id=transcript['user_id'],
                        audio_timestamp=transcript.get('timestamp', time.time()), # Use the correct keyword
                        channel_type="voice", # Log as voice
                        channel_id=str(self.voice_client.channel.id) if self.voice_client and self.voice_client.channel else None
                    )

                # --- Call LLM Brain for Action Decision based on Voice Turn ---
                brain_action_taken = False
                # Only proceed if there are transcripts to analyze
                if transcripts_to_process:
                    try:
                        # --- Preprocess Turn Text for Brain (Resolve Aliases) ---
                        turn_text_for_brain_raw = "\n".join([f"{t['canonical_name']} ({t['user_id']}): {t['text']}" for t in transcripts_to_process]) # Use canonical_name
                        processed_turn_text = turn_text_for_brain_raw # Start with raw text
                        # Apply similar alias resolution logic as in main.py, but on the combined turn text
                        potential_target_match_voice = re.search(r"(?:dm|tell|ask|kick|disconnect|call)\s+@?([\w\s]+)", turn_text_for_brain_raw, re.IGNORECASE | re.MULTILINE)
                        if potential_target_match_voice:
                            potential_alias_voice = potential_target_match_voice.group(1).strip().lower()
                            # Check if the potential alias is just a user ID first
                            if not re.fullmatch(r'\d{17,}', potential_alias_voice): # Don't resolve if it's likely an ID
                                logger.debug(f"Potential target alias found in voice turn text: '{potential_alias_voice}'")
                                resolved_main_name_voice = get_main_name_from_alias(potential_alias_voice)
                                if resolved_main_name_voice and resolved_main_name_voice.lower() != potential_alias_voice:
                                    logger.info(f"Resolved alias '{potential_alias_voice}' to main name '{resolved_main_name_voice}' for voice brain input.")
                                    # Replace first occurrence in the combined text (simple approach)
                                    processed_turn_text = turn_text_for_brain_raw.replace(potential_target_match_voice.group(1), resolved_main_name_voice, 1)
                                    logger.debug(f"Processed voice turn text for brain: '{processed_turn_text[:100]}...'")
                                else:
                                    logger.debug(f"No specific main name found for potential voice alias '{potential_alias_voice}', using original turn text.")
                            else:
                                logger.debug(f"Potential voice alias '{potential_alias_voice}' looks like a user ID, skipping resolution.")

                        # Format context for the brain using the processed text
                        voice_channel_name = self.voice_client.channel.name if self.voice_client and self.voice_client.channel else "Unknown Voice Channel"
                        voice_channel_id = str(self.voice_client.channel.id) if self.voice_client and self.voice_client.channel else "Unknown"
                        turn_context_string = f"Conversation turn in voice channel '{voice_channel_name}' ({voice_channel_id}):\n{processed_turn_text}" # Use processed_turn_text

                        logger.info(f"Querying LLM Brain for action based on voice turn...")
                        # Mark brain processing start
                        mark_latency_timestamp("brain_start")
                        brain_action_details = await llm_brain.process_brain_request(
                            prompt=turn_context_string, # Pass the potentially modified context string
                            bot=self.bot,
                            sink=self, # Pass self (sink) for context
                            system_prompt=self.system_prompt, # Pass main system prompt
                            effective_text_channel=self.text_channel # Pass the default text channel for confirmations/errors
                        )
                        # Mark brain processing end
                        mark_latency_timestamp("brain_end")

                        # --- Handle Brain Action Result ---
                        if brain_action_details:
                             action_taken = brain_action_details.get('action', 'error')
                             logger.info(f"Brain Action Result: {action_taken}, Details: {brain_action_details}")

                             if action_taken in ['call_user', 'disconnect_user']:
                                  if brain_action_details.get('success', False):
                                       logger.info(f"Brain successfully executed action: {action_taken}. Turn processing complete.")
                                       brain_action_taken = True # Mark action as successfully handled
                                  else:
                                       logger.warning(f"Brain action '{action_taken}' failed execution. Raw: {brain_action_details.get('raw_decision')}")
                                       # Action was attempted but failed, still mark as 'handled' to prevent fallback response
                                       brain_action_taken = True
                             elif action_taken == 'dm_user':
                                  # DM action needs to be executed here for voice commands
                                  if brain_action_details.get('success', False):
                                       logger.info("Brain decided 'DM User' for voice command. Executing DM...")
                                       dm_success = await self._execute_dm_action(brain_action_details)
                                       if dm_success:
                                            logger.info("DM action executed successfully for voice command.")
                                            brain_action_taken = True
                                       else:
                                            logger.warning("DM action execution failed for voice command.")
                                            brain_action_taken = True # Still mark as handled to prevent fallback
                                  else:
                                       logger.error(f"Brain action 'dm_user' indicated success=False unexpectedly. Raw: {brain_action_details.get('raw_decision')}")
                                       brain_action_taken = True # Mark as handled
                             elif action_taken == 'no_action':
                                  logger.info("Brain decided 'No Action'.")
                                  brain_action_taken = False # Explicitly false so conversational response proceeds
                             elif action_taken == 'error':
                                  logger.error(f"Brain processing resulted in an error: {brain_action_details.get('details')}. Raw: {brain_action_details.get('raw_decision')}")
                                  brain_action_taken = False # Proceed with standard response as fallback
                             else:
                                  logger.warning(f"Unknown action returned from brain: {action_taken}. Proceeding with standard response.")
                                  brain_action_taken = False # Proceed with standard response
                        else:
                             # Should not happen if process_brain_request always returns a dict, but handle defensively
                             logger.error("process_brain_request returned None unexpectedly.")
                             brain_action_taken = False # Proceed with standard response

                    except Exception as brain_exec_err:
                         # Catch errors during the call to process_brain_request itself
                         logger.error(f"Error calling process_brain_request for voice turn: {brain_exec_err}", exc_info=True)
                         brain_action_taken = False # Proceed with standard response as fallback

                # --- If Brain Didn't Take Action, Proceed with Conversational Response Decision ---
                if not brain_action_taken:
                    # Mark decision point (brain decided no action, proceeding to response decision)
                    mark_latency_timestamp("decision_end")
                    
                    logger.info("LLM Brain took no specific action for voice turn, checking if Luna should respond.")
                    
                    # For random responses, always proceed to generate response
                    should_luna_respond = is_random_trigger
                    
                    # For mention triggers, always respond (forced response)
                    if is_mention_trigger:
                        should_luna_respond = True
                        logger.info("Luna mentioned - forcing response")
                    
                    # For group silence triggers, use the decision logic to determine if Luna should respond
                    elif not is_random_trigger:
                        logger.info("Group silence detected - analyzing conversation to decide if Luna should respond")
                        # Prepare data for decision analysis
                        if not transcripts_to_process:
                            logger.warning("No transcripts available for group silence analysis.")
                            return
                        
                        # Use the last utterance for decision analysis
                        last_utterance = transcripts_to_process[-1]
                        text_for_decision = last_utterance['text']
                        user_id_for_decision = last_utterance['user_id']
                        
                        # Get conversation context for decision
                        recent_raw_history = self.conversation_history[-FAST_SESSION_LIMIT:] if self.conversation_history else []
                        speaker_turn_history = self.get_recent_speaker_turns(count=5)
                        
                        # Import the decision function
                        from llm_response.decision import should_respond
                        
                        # Check if Luna should respond based on the conversation context
                        should_luna_respond = await should_respond(
                            text=text_for_decision,
                            current_speaker_id=str(user_id_for_decision),
                            conversation_history=recent_raw_history,
                            speaker_turn_history=speaker_turn_history,
                            is_currently_speaking=self.is_speaking,
                            channel_id=self.voice_client.channel.id if self.voice_client and self.voice_client.channel else None,
                            confidence_threshold=None  # Use default
                        )
                        
                        if should_luna_respond:
                            logger.info("Decision: Luna will respond to group silence")
                        else:
                            logger.info("Decision: Luna will not respond to group silence")
                    
                    # If Luna should not respond, exit early
                    if not should_luna_respond:
                        logger.info("Luna decided not to respond to this turn.")
                        return

                    # Prepare data for the existing response logic (using last utterance)
                    if not transcripts_to_process: # Should generally not happen if we got here
                         logger.warning("No transcripts available to determine last utterance for conversational response.")
                         return

                    last_utterance_for_response = transcripts_to_process[-1]
                    text_for_response = last_utterance_for_response['text']
                    user_id_for_response = last_utterance_for_response['user_id']
                    display_name_for_response = last_utterance_for_response['canonical_name'] # Use canonical_name

                    # For random responses, create a more engaging text
                    if is_random_trigger:
                        text_for_response = "Randomly joining the conversation"
                        user_id_for_response = "system"
                        display_name_for_response = "System"

                    logger.info(f"Proceeding with conversational response generation for: {display_name_for_response}")

                    # Call the existing response logic
                    try:
                        # Extract latency timestamps from the last utterance
                        audio_detected_time = last_utterance_for_response.get("audio_detected_time")
                        transcription_end_time = last_utterance_for_response.get("transcription_end_time")

                        # Check if chat_session exists and has messages
                        if self.chat_session is None or not isinstance(self.chat_session, dict) or "messages" not in self.chat_session:
                            logger.warning("Voice chat session is missing or invalid. Creating a new one.")
                            # Create a new session
                            session_id = f"voice_channel_{int(time.time())}"
                            self.chat_session = {
                                "id": session_id,
                                "messages": []
                            }
                            # No system prompt for Ollama - using custom modelfile instead
                            logger.info(f"New voice chat session initialized without system prompt (using Ollama custom modelfile). Session has {len(self.chat_session['messages'])} messages.")
                        else:
                            # Log the current state of the chat session
                            logger.info(f"Using existing voice chat session with {len(self.chat_session['messages'])} messages")
                            if len(self.chat_session["messages"]) > 0:
                                logger.info(f"First message in voice session: role={self.chat_session['messages'][0].get('role')}, content={self.chat_session['messages'][0].get('content')[:50]}...")
                            if len(self.chat_session["messages"]) > 1:
                                logger.info(f"Second message in voice session: role={self.chat_session['messages'][1].get('role')}, content={self.chat_session['messages'][1].get('content')[:50]}...")

                        await llm_response.process_user_message( # Ensure we call the function from the module
                            bot=self.bot,
                            text=text_for_response,
                            user_id=user_id_for_response,
                            conversation_history=self.conversation_history, # Still pass for logging/context
                            system_prompt=self.system_prompt, # Will be ignored if session provided
                            sink=self,
                            force_respond=True, # Always force respond if we got to this point (decision was already made)
                            display_name=display_name_for_response,
                            text_channel=self.text_channel,
                            image_analysis_text=None, # No image analysis in voice turns currently
                            chat_session=self.chat_session, # Pass the stateful chat session
                            # Pass latency timestamps
                            audio_detected_time=audio_detected_time,
                            transcription_end_time=transcription_end_time
                        )
                    except Exception as e:
                         logger.error(f"Error calling process_user_message from _process_collected_turn (fallback): {e}", exc_info=True)

                logger.info("Turn processing complete.")
                
                # Complete latency tracking and generate report
                try:
                    from llm_response.latency_tracker import complete_latency_tracking, generate_latency_report, format_latency_report_for_discord
                    from llm_response.config import PROMPT_LOG_CHANNEL_ID
                    import discord
                    from datetime import datetime
                    
                    metrics = complete_latency_tracking()
                    if metrics and PROMPT_LOG_CHANNEL_ID:
                        # Generate and send comprehensive latency report
                        report = generate_latency_report(metrics)
                        formatted_report = format_latency_report_for_discord(report)
                        
                        # Send to latency reports channel
                        try:
                            channel = self.bot.get_channel(int(PROMPT_LOG_CHANNEL_ID))
                            if channel:
                                embed = discord.Embed(
                                    title="🎤 Luna Voice Latency Report",
                                    description=formatted_report,
                                    color=0x1e90ff,
                                    timestamp=datetime.utcnow()
                                )
                                await channel.send(embed=embed)
                                logger.info(f"📊 Sent latency report for turn {metrics.turn_id} to channel {PROMPT_LOG_CHANNEL_ID}")
                            else:
                                logger.warning(f"📊 Could not find latency reports channel {PROMPT_LOG_CHANNEL_ID}")
                        except Exception as channel_err:
                            logger.error(f"📊 Failed to send latency report to Discord: {channel_err}")
                    else:
                        logger.debug("📊 No latency metrics to report or no reports channel configured")
                except Exception as latency_err:
                    logger.error(f"📊 Error generating latency report: {latency_err}", exc_info=True)
                
            finally:
                # Reset the flag after processing if it was a mention-triggered turn
                if triggered_by_mention:
                    self.mention_processing_active = False

    # --- Helper methods for user/member lookup ---

    async def _find_member_from_identifier(self, identifier: str) -> discord.Member | None:
        """Helper to find a guild member by ID, mention, or name/nick."""
        target_member = None
        user_id_to_fetch = None
        guild = self.voice_client.guild if self.voice_client else None
        if not guild:
            logger.error("_find_member_from_identifier: Cannot find member without guild context.")
            return None

        # 1. Try ID (mention or plain)
        mention_match = re.search(r"<@!?(\d+)>", identifier)
        plain_id_match = re.search(r"[\[\(]?(\d{17,})[\]\)]?", identifier)
        if mention_match:
            try: user_id_to_fetch = int(mention_match.group(1))
            except ValueError: pass
        elif plain_id_match:
            try: user_id_to_fetch = int(plain_id_match.group(1))
            except ValueError: pass

        if user_id_to_fetch:
            try:
                target_member = await guild.fetch_member(user_id_to_fetch)
            except (discord.NotFound, discord.HTTPException):
                target_member = None

        # 2. Fallback to name/nick
        if not target_member:
            name_to_search = re.sub(r'[<@!>#()\[\]]', '', identifier).strip()
            if name_to_search.lower().startswith("user "):
                name_to_search = name_to_search[5:].strip()
            if name_to_search:
                # Iterate through guild members for case-insensitive match
                for member in guild.members:
                    if member.name.lower() == name_to_search.lower() or \
                       (member.nick and member.nick.lower() == name_to_search.lower()):
                        target_member = member
                        break
        return target_member

    # Add this method to the DiscordSink class
    def get_display_name(self, user_id: int) -> str:
        """Gets the canonical main name for a user ID, falling back to User_ID."""
        main_name = get_main_name_by_id(user_id)
        if main_name:
            return main_name
        # Fallback if ID not in profiles
        logger.warning(f"User ID {user_id} not found in USER_PROFILES. Falling back to User_{user_id}.")
        return f"User_{user_id}"

    # _find_user_from_identifier function moved to utils.py to resolve circular import

    def _schedule_next_random_response(self):
        """Schedule the next random response at a random interval"""
        import random
        # Random interval between 5-15 minutes (300-900 seconds)
        interval = random.randint(300, 900)
        logger.info(f"Scheduling next random response in {interval} seconds ({interval/60:.1f} minutes)")
        
        # Cancel previous timer if it exists
        if self.random_response_timer:
            self.random_response_timer.cancel()
        
        # Schedule new timer
        self.random_response_timer = asyncio.get_event_loop().call_later(
            interval, 
            lambda: asyncio.create_task(self._trigger_random_response())
        )
    
    async def _trigger_random_response(self):
        """Trigger a random response if conditions are met"""
        try:
            # Only trigger if there's been recent activity and no current processing
            current_time = time.monotonic()
            time_since_activity = current_time - self.last_activity_time
            
            # Only respond if there's been some recent activity (within last 2 minutes)
            # but not too recent (at least 30 seconds ago)
            if 30 < time_since_activity < 120 and not self.mention_processing_active:
                logger.info("Triggering random response")
                self.random_response_active = True
                
                # Create a fake transcript for the random response
                await self._process_collected_turn(triggered_by_random=True)
                
                self.random_response_active = False
            else:
                logger.debug(f"Skipping random response - time since activity: {time_since_activity:.1f}s, mention processing: {self.mention_processing_active}")
                
        except Exception as e:
            logger.error(f"Error triggering random response: {e}", exc_info=True)
            self.random_response_active = False
        finally:
            # Schedule the next random response
            self._schedule_next_random_response()

    def cleanup(self):
        """Clean up resources when the sink is no longer needed"""
        # Cancel random response timer
        if self.random_response_timer:
            self.random_response_timer.cancel()
            self.random_response_timer = None
            logger.info("Cancelled random response timer during cleanup")

# --- Standalone helper functions (if any) ---

def is_luna_mentioned(message, bot_user):
    # Check all mention types
    return any([
        bot_user.mentioned_in(message),
        re.search(rf'@?{re.escape(bot_user.name)}', message.content, re.I),
        'luna' in message.content.lower() and (
            message.reference and
            message.reference.resolved.author == bot_user
        )
    ])