import os
import logging
import re
import discord
import time
import httpx
import base64
import mimetypes
import asyncio
from openai import AsyncOpenAI, OpenAI # Import AsyncOpenAI
import io
from PIL import Image

# Import necessary components from other modules within the package
from .config import (
    SCREENSHOT_COOLDOWN, CALL_NOTIFICATION_CHANNEL_ID_STR, # Removed USER_ALIASES
    LM_STUDIO_VISION_URL, LM_STUDIO_VISION_API_KEY, VISION_MODEL,
    IMAGE_ANALYSIS_MAX_TOKENS, IMAGE_ANALYSIS_TEMP, IMAGE_DOWNLOAD_TIMEOUT,
    CALL_MSG_MAX_OUTPUT_TOKENS, CALL_MSG_TEMP, TRANSCRIPT_LOG_CHANNEL_ID,
    PROMPT_LOG_CHANNEL_ID # Added for process_image_mention logging
)
from .initialization import get_lm_studio_client, create_lm_studio_client
# Removed safe_model_call import - using direct client calls instead
from .db_logger import log_message_to_db
# Import process_user_message carefully to avoid circular dependency if possible
# If commands.py is imported by processing.py, this will cause issues.
# Consider refactoring process_image_mention or passing process_user_message as an arg.
# For now, assume it might be imported later or handled differently.
# from .processing import process_user_message # Commented out for now

# Import necessary components from outside the package
from text_to_speech import StreamingSpeechPlayer
from utils import remove_emojis, format_datetime # format_datetime might not be needed here anymore
from screenshot_util import optimized_screenshot_manager, analyze_image_url_fast

logger = logging.getLogger(__name__)

# Track screenshot usage to prevent abuse
last_screenshot_time = 0

# --- Screenshot Command Handling (Requires Multimodal Model - Placeholder/Disabled) ---
async def handle_screenshot_command(sink, text, user_id, display_name=None, text_channel=None):
    """Handles screenshot command using the new ultra-fast optimized system."""
    global last_screenshot_time

    # Rate limit screenshots
    current_time = time.time()
    if current_time - last_screenshot_time < SCREENSHOT_COOLDOWN:
        cooldown_message = f"Please wait a moment before requesting another screenshot."
        if text_channel:
            await text_channel.send(cooldown_message)
        else:
            # Voice response
            voice_client = sink.bot.voice_clients[0] if sink.bot.voice_clients and len(sink.bot.voice_clients) > 0 else None
            if voice_client:
                if voice_client.is_playing():
                    voice_client.stop()
                sink.is_speaking = True
                player = StreamingSpeechPlayer(
                    voice_client,
                    after_play_callback=lambda e: sink._after_play(e, user_id)
                )
                await player.start()
                await player.add_text(cooldown_message)
                await player.finalize()
        return True

    # Extract custom analysis prompt if present
    custom_prompt = None
    
    # Check for test mode
    if "test api" in text.lower():
        # Note: test_api_performance function was removed in optimization
        await text_channel.send("API performance test no longer available - using optimized system")
        return True
        
    # Check for tiny test mode
    if "tiny test" in text.lower():
        processing_message = "Testing with 64x64 image..."
        if text_channel:
            status_msg = await text_channel.send(processing_message)
        
        result = await optimized_screenshot_manager.capture_and_analyze_fast("tiny test")
        
        if result["success"]:
            total_time = result.get("total_time", 0)
            analysis = result["analysis"]
            response = f"Tiny test result: {analysis} (completed in {total_time:.2f}s)"
        else:
            response = f"Tiny test failed: {result.get('error', 'Unknown error')}"
            
        if text_channel:
            if 'status_msg' in locals():
                await status_msg.edit(content=response)
            else:
                await text_channel.send(response)
        return True
    
    prompt_patterns = [
        r"screenshot (?:and|&) (?:analyze|tell me about|describe|explain|what do you see in|look at|find|search for|check for|see if there's|can you see|is there) (.*)",
        r"take a screenshot (?:and|&) (?:analyze|tell me about|describe|explain|what do you see in|look at|find|search for|check for|see if there's|can you see|is there) (.*)",
        r"screenshot (?:and|&) (find|look for|search for|locate|spot|identify) (.*)",
        r"screenshot (?:and tell me about|and look for|and find) (.*)",
        r"look for (.*?)(?:\.|\?|$|in the|on the)",
        r"find (.*?)(?:\.|\?|$|in the|on the)",
        r"search for (.*?)(?:\.|\?|$|in the|on the)"
    ]
    
    for pattern in prompt_patterns:
        match = re.search(pattern, text.lower())
        if match:
            if len(match.groups()) == 1:
                custom_prompt = match.group(1).strip()
            elif len(match.groups()) >= 2:
                action = match.group(1).strip()
                target = match.group(2).strip()
                custom_prompt = f"{action} {target}"
            break

    # Let the user know we're taking a screenshot
    processing_message = "Analyzing your screen..." if not custom_prompt else f"Looking for '{custom_prompt}'..."
    
    if text_channel:
        status_msg = await text_channel.send(processing_message)
    else:
        # Voice response
        voice_client = sink.bot.voice_clients[0] if sink.bot.voice_clients and len(sink.bot.voice_clients) > 0 else None
        if voice_client:
            if voice_client.is_playing(): voice_client.stop()
            sink.is_speaking = True
            player = StreamingSpeechPlayer(voice_client, after_play_callback=lambda e: sink._after_play(e, user_id))
            await player.start()
            await player.add_text(processing_message)
            await player.finalize()

    # Use the new ultra-fast optimized screenshot system with context
    if custom_prompt:
        # Add context about it being Gavin's screen to custom prompts
        contextual_prompt = f"I'm looking at Gavin's screen right now. {custom_prompt}"
    else:
        contextual_prompt = None
    
    result = await optimized_screenshot_manager.capture_and_analyze_fast(contextual_prompt)
    
    if not result["success"]:
        error_message = f"Screenshot failed: {result.get('error', 'Unknown error')}"
        if text_channel:
            await text_channel.send(error_message)
        else:
            voice_client = sink.bot.voice_clients[0] if sink.bot.voice_clients and len(sink.bot.voice_clients) > 0 else None
            if voice_client:
                if voice_client.is_playing(): voice_client.stop()
                sink.is_speaking = True
                player = StreamingSpeechPlayer(voice_client, after_play_callback=lambda e: sink._after_play(e, user_id))
                await player.start()
                await player.add_text(error_message)
                await player.finalize()
        return True

    analysis = result["analysis"]
    total_time = result.get("total_time", 0)
    
    # Update rate limit
    last_screenshot_time = time.time()

    # Add analysis to chat session for context
    if sink and hasattr(sink, 'chat_session') and sink.chat_session and "messages" in sink.chat_session:
        sink.chat_session["messages"].append({"role": "assistant", "content": analysis})

    # Performance info
    perf_info = f" (completed in {total_time:.2f}s)" if total_time > 0 else ""
    
    # Deliver the response
    if text_channel and sink and sink.bot.voice_clients:
        # In voice: respond with both TTS and text
        try:
            if status_msg:
                await status_msg.edit(content=f"{analysis}{perf_info}")
            else:
                await text_channel.send(f"{analysis}{perf_info}")
            
            # Add to conversation history using the same pattern as regular Luna responses
            if hasattr(sink, '_add_to_history'):
                await sink._add_to_history("assistant", analysis, "assistant", channel_type="text", channel_id=str(text_channel.id))
            
            # Log to database just like regular Luna responses
            log_message_to_db(sink.bot.user.id, "assistant", analysis, time.time(), "text", text_channel.id)
            
        except Exception as e:
            logger.error(f"Error sending screenshot response to text channel: {e}")
            await text_channel.send(analysis)
        
        # TTS response
        voice_client = sink.bot.voice_clients[0] if sink.bot.voice_clients and len(sink.bot.voice_clients) > 0 else None
        if voice_client:
            if voice_client.is_playing(): voice_client.stop()
            sink.is_speaking = True
            player = StreamingSpeechPlayer(voice_client, after_play_callback=lambda e: sink._after_play(e, user_id))
            await player.start()
            await player.add_text(analysis)
            await player.finalize()
            if hasattr(sink, '_add_to_history'):
                await sink._add_to_history("assistant", analysis, "assistant")
            
            # Log voice response to database just like regular Luna responses
            log_message_to_db(sink.bot.user.id, "assistant", analysis, time.time(), "voice", voice_client.channel.id if voice_client else None)
    elif text_channel:
        # Only text
        try:
            if status_msg:
                await status_msg.edit(content=f"{analysis}{perf_info}")
            else:
                await text_channel.send(f"{analysis}{perf_info}")
            
            # Add to conversation history using the same pattern as regular Luna responses
            if hasattr(sink, '_add_to_history'):
                await sink._add_to_history("assistant", analysis, "assistant", channel_type="text", channel_id=str(text_channel.id))
            
            # Log to database just like regular Luna responses
            # Get bot user ID from sink or use a fallback method
            bot_user_id = sink.bot.user.id if sink and hasattr(sink, 'bot') and sink.bot.user else "assistant"
            log_message_to_db(bot_user_id, "assistant", analysis, time.time(), "text", text_channel.id)
            
        except Exception as e:
            logger.error(f"Error sending screenshot response to text channel: {e}")
            await text_channel.send(analysis)
    else:
        # Only voice
        voice_client = sink.bot.voice_clients[0] if sink.bot.voice_clients and len(sink.bot.voice_clients) > 0 else None
        if voice_client:
            if voice_client.is_playing(): voice_client.stop()
            sink.is_speaking = True
            player = StreamingSpeechPlayer(voice_client, after_play_callback=lambda e: sink._after_play(e, user_id))
            await player.start()
            await player.add_text(analysis)
            await player.finalize()
            if hasattr(sink, '_add_to_history'):
                await sink._add_to_history("assistant", analysis, "assistant")
            
            # Log voice response to database just like regular Luna responses
            log_message_to_db(sink.bot.user.id, "assistant", analysis, time.time(), "voice", voice_client.channel.id if voice_client else None)
    
    return True

# --- Call Message Generation ---
async def generate_call_message(target_name: str, system_prompt: str) -> str:
    """Generates a short, engaging message body for calling a user."""
    lm_studio_client = get_lm_studio_client() # Get the initialized client
    if not lm_studio_client:
        logger.error("Cannot generate call message: LM Studio client not initialized.")
        return f"Hey {target_name}, someone's asking for you!" # Fallback

    prompt = f"""You need to generate a short, casual message body to notify '{target_name}' that someone in the voice chat is asking for them. Keep it brief and friendly, encouraging them to join the voice channel.

    Examples:
    - "Hey {target_name}, hop in the voice channel! We need you."
    - "Yo {target_name}, someone's asking for you in voice chat."
    - "{target_name}! Get in here, you're being summoned!"
    - "Paging {target_name}, you're wanted in the voice channel."

    Generate ONLY the message body below (don't include the name again unless it fits naturally):
    """

    try:
        # Use a stateless call for this simple generation
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": prompt}
        ]

        # Use the ultra-fast generate endpoint for better latency
        from llm_response.processing import _ultra_fast_ollama_non_streaming_call
        
        message_body = await _ultra_fast_ollama_non_streaming_call(
            messages=messages,
            model=os.environ.get("LM_STUDIO_MODEL_NAME", "luna:latest"),
            temperature=CALL_MSG_TEMP,
            max_tokens=CALL_MSG_MAX_OUTPUT_TOKENS,
            bot=None  # No bot instance available in commands context
        )

        message_body = message_body.strip() if message_body else ""
        if not message_body:
            raise ValueError("LLM returned empty message body.")
        logger.info(f"Generated call message body for {target_name}: {message_body}")
        return message_body
    except Exception as e:
        logger.error(f"Error generating call message body for {target_name}: {e}", exc_info=True)
        # Fallback message
        return f"someone's asking for you!"

# --- Image Analysis for URLs and Attachments ---
async def analyze_image_url_optimized(image_url: str) -> str | None:
    """Fast image URL analysis using the optimized system."""
    try:
        logger.info(f"Starting optimized analysis of image URL: {image_url}")
        
        # Use our new optimized image URL analysis with context
        result = await analyze_image_url_fast(image_url, "I'm looking at an image that Gavin shared. Describe this image briefly in 2-3 sentences.")
        
        logger.info(f"Image URL analysis completed successfully")
        return result
                
    except Exception as e:
        logger.error(f"Failed to analyze image URL: {e}")
        return f"Could not analyze image: {e}"

# --- Process Image Mention (Simplified) ---
async def process_image_mention(*, message, image_url, bot, system_prompt, process_user_message_func):
    """Process a message that mentions Luna and includes an image attachment using optimized analysis."""
    logger.info(f"Processing image mention from {message.author.display_name} with URL: {image_url}")

    # Step 1: Analyze the image using optimized system
    image_analysis = await analyze_image_url_optimized(image_url)

    if not image_analysis:
        await message.channel.send("Sorry, I couldn't analyze that image. My vision capabilities might be offline.")
        return

    logger.info(f"Image analysis complete: {image_analysis[:100]}...")

    # Step 2: Get or create a chat session for this channel
    if not hasattr(bot, 'chat_sessions'):
        bot.chat_sessions = {}

    session_key = f"text_{message.channel.id}"
    chat_session = bot.chat_sessions.get(session_key)
    lm_studio_client = get_lm_studio_client()

    if not chat_session and lm_studio_client:
        logger.info(f"Creating new chat session for channel {message.channel.id}")
        session_id = f"channel_{message.channel.id}_{int(time.time())}"
        chat_session = {
            "id": session_id,
            "messages": []
        }
        bot.chat_sessions[session_key] = chat_session
    elif not lm_studio_client:
        logger.error("Cannot create chat session for image mention: LM Studio client not initialized.")
        await message.channel.send("Sorry, I can't process this right now due to an internal issue.")
        return

    # Step 3: Process the message with the image analysis
    await process_user_message_func(
        bot=bot,
        text=message.content,
        user_id=message.author.id,
        conversation_history=[],
        system_prompt=system_prompt,
        sink=None,
        force_respond=True,
        display_name=message.author.display_name,
        text_channel=message.channel,
        image_analysis_text=image_analysis,
        chat_session=chat_session
    )
    
    # Add image analysis to chat session for context
    if chat_session and "messages" in chat_session:
        chat_session["messages"].append({"role": "assistant", "content": image_analysis})
    
    logger.info(f"Completed processing image mention from {message.author.display_name}")