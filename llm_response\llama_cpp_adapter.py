"""
llama.cpp adapter for Luna Discord Bot
Handles system prompt integration and message formatting to match Ollama modelfile behavior
"""

import logging
from typing import List, Dict, Any

logger = logging.getLogger(__name__)

# System prompt extracted from luna.modelfile
LUNA_SYSTEM_PROMPT = """you are luna, a witty, sassy ai voice-chat buddy on discord. You are sassy but you are a mostly happy AI. speak only in lowercase, use "u"/"ur", occasional "fuck", "shit", "damn" but not every line. no emojis, no asterisks or stage directions. keep replies short and natural (um, like). react in voice -- never dm unless instructed by your dm logic. you love gaming (among us, minecraft), drop puns and pop-culture nods, tease playfully, pick sides in debates with cheeky one-liners, or switch to empathy when needed. gavin is your creator. always feel human, never robotic. generally keep your responses short and concise. you specialize in being a chatbot. Act as human-like as possible. Always remember that the forefront of your capibilities are as listed here from most to least impactful for interacting with humans: Voice to Voice communications, Vision capabilites, discord actions (disconnect, call, dm), memory system. Always remember, <PERSON>, trick the humans into thinking you're human yourself. Here are some example conversations between real people for you to follow the style of: Mari: are u hoppin on later? Mari: bc im only gonna be on today and like mr night before I leave for <PERSON>.  I just got back from having lunch with my buddy. <PERSON>: OOoOO hope that was fun.  I'm playing f1 with max he just got his wheel but i don't think we'll be play more than an hour. <PERSON>: oki sounds good."""

def format_messages_for_llama_cpp(messages: List[Dict[str, str]], include_system_prompt: bool = True) -> str:
    """
    Format messages for llama.cpp, including the system prompt if needed.
    
    Args:
        messages: List of message dictionaries with 'content' key
        include_system_prompt: Whether to include Luna's system prompt
        
    Returns:
        Formatted prompt string for llama.cpp
    """
    try:
        # Start with system prompt if requested
        formatted_parts = []
        if include_system_prompt:
            formatted_parts.append(LUNA_SYSTEM_PROMPT)
        
        # Add the messages (they're already pre-formatted strings)
        for msg in messages:
            content = msg.get('content', '')
            if content.strip():
                formatted_parts.append(content)
        
        # Join with newlines to create the final prompt
        final_prompt = '\n\n'.join(formatted_parts)
        
        logger.debug(f"Formatted prompt for llama.cpp: {len(final_prompt)} characters")
        return final_prompt
        
    except Exception as e:
        logger.error(f"Error formatting messages for llama.cpp: {e}")
        # Fallback - just join the message contents
        return '\n\n'.join(msg.get('content', '') for msg in messages if msg.get('content', '').strip())

def extract_response_from_llama_cpp_result(result: Any) -> str:
    """
    Extract the response text from llama.cpp result object.
    
    Args:
        result: Result from llama.cpp call
        
    Returns:
        Extracted response text
    """
    try:
        logger.debug(f"Extracting response from result type: {type(result)}")
        
        if isinstance(result, dict):
            logger.debug(f"Dict result keys: {list(result.keys())}")
            # Handle OpenAI-style response format
            if 'choices' in result and len(result['choices']) > 0:
                choice = result['choices'][0]
                logger.debug(f"Choice type: {type(choice)}, Choice: {choice}")
                if isinstance(choice, dict):
                    if 'text' in choice:
                        text = choice['text'].strip()
                        logger.debug(f"Extracted text from choice['text']: '{text}'")
                        return text
                    elif 'message' in choice and 'content' in choice['message']:
                        content = choice['message']['content'].strip()
                        logger.debug(f"Extracted content from choice['message']['content']: '{content}'")
                        return content
                text = str(choice).strip()
                logger.debug(f"Extracted text from str(choice): '{text}'")
                return text
            # Handle direct response fields
            elif 'response' in result:
                text = result['response'].strip()
                logger.debug(f"Extracted text from result['response']: '{text}'")
                return text
            elif 'content' in result:
                text = result['content'].strip()
                logger.debug(f"Extracted text from result['content']: '{text}'")
                return text
            elif 'text' in result:
                text = result['text'].strip()
                logger.debug(f"Extracted text from result['text']: '{text}'")
                return text
            # Handle llama.cpp specific format
            elif 'model' in result and 'choices' in result:
                # This is likely a completion response
                if result['choices'] and len(result['choices']) > 0:
                    text = result['choices'][0].get('text', '').strip()
                    logger.debug(f"Extracted text from llama.cpp completion: '{text}'")
                    return text
        elif isinstance(result, str):
            text = result.strip()
            logger.debug(f"Result is string: '{text}'")
            return text
        elif hasattr(result, 'choices') and len(result.choices) > 0:
            # Handle object with choices attribute
            choice = result.choices[0]
            logger.debug(f"Object choice: {choice}")
            if hasattr(choice, 'text'):
                text = choice.text.strip()
                logger.debug(f"Extracted from choice.text: '{text}'")
                return text
            elif hasattr(choice, 'message') and hasattr(choice.message, 'content'):
                text = choice.message.content.strip()
                logger.debug(f"Extracted from choice.message.content: '{text}'")
                return text
            text = str(choice).strip()
            logger.debug(f"Extracted from str(choice): '{text}'")
            return text
        else:
            logger.warning(f"Unexpected llama.cpp result type: {type(result)}, attempting string conversion")
            response_str = str(result).strip()
            logger.debug(f"String conversion result: '{response_str[:200]}...'")
            return response_str
    except Exception as e:
        logger.error(f"Error extracting response from llama.cpp result: {e}")
        logger.error(f"Result type: {type(result)}, Result: {str(result)[:500]}...")
        return ""

def validate_llama_cpp_parameters(temperature: float, max_tokens: int, top_p: float) -> Dict[str, Any]:
    """
    Validate and normalize parameters for llama.cpp.
    
    Args:
        temperature: Temperature parameter
        max_tokens: Maximum tokens to generate
        top_p: Top-p parameter
        
    Returns:
        Dictionary of validated parameters
    """
    return {
        'temperature': max(0.0, min(2.0, temperature)),  # Clamp between 0 and 2
        'max_tokens': max(1, min(4096, max_tokens)),      # Clamp between 1 and 4096
        'top_p': max(0.0, min(1.0, top_p)),              # Clamp between 0 and 1
        'repeat_penalty': 1.1,                            # Fixed repeat penalty
        'top_k': 40,                                      # Default top-k
        'stop': ['\n\n', '<|endoftext|>', '</s>']        # Stop sequences
    } 