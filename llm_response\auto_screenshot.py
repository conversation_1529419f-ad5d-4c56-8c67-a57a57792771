import asyncio
import time
import logging
from typing import Optional, Dict, Any, List
from collections import deque

from .config import AUTO_SCREENSHOT_ENABLED, AUTO_SCREENSHOT_INTERVAL, AUTO_SCREENSHOT_MAX_STORAGE
from .db_logger import log_message_to_db
from screenshot_util import optimized_screenshot_manager

logger = logging.getLogger(__name__)

class AutoScreenshotManager:
    """Manages automatic background screenshots for Luna's contextual awareness."""
    
    def __init__(self):
        self.is_running = False
        self.task: Optional[asyncio.Task] = None
        self.bot = None
        self.background_observations: deque = deque(maxlen=AUTO_SCREENSHOT_MAX_STORAGE)
        self.last_screenshot_content = ""
        self.consecutive_similar_count = 0
        self.active_channels: set = set()
        self.luna_is_responding = False  # Flag to pause screenshots during response generation
        
    def set_bot(self, bot):
        """Set the Discord bot instance for logging."""
        self.bot = bot
        
    def add_active_channel(self, channel_id: int):
        """Add a channel where <PERSON> is active (voice chat, recent text activity)."""
        self.active_channels.add(channel_id)
        logger.info(f"Auto-screenshot: Added active channel {channel_id}")
        
    def remove_active_channel(self, channel_id: int):
        """Remove a channel from active monitoring."""
        self.active_channels.discard(channel_id)
        logger.info(f"Auto-screenshot: Removed active channel {channel_id}")
        
    def pause_for_response(self):
        """Pause auto-screenshots while Luna is generating a response."""
        self.luna_is_responding = True
        logger.debug("Auto-screenshot: Paused for Luna response generation")
        
    def resume_after_response(self):
        """Resume auto-screenshots after Luna finishes responding."""
        self.luna_is_responding = False
        logger.debug("Auto-screenshot: Resumed after Luna response generation")
        
    def is_active(self) -> bool:
        """Check if Luna should be taking automatic screenshots."""
        return len(self.active_channels) > 0 and not self.luna_is_responding
        
    async def start(self):
        """Start the automatic screenshot background task."""
        if not AUTO_SCREENSHOT_ENABLED:
            logger.info("Auto-screenshot disabled in config")
            return
            
        if self.is_running:
            logger.warning("Auto-screenshot already running")
            return
            
        self.is_running = True
        self.task = asyncio.create_task(self._background_loop())
        logger.info(f"🔄 Auto-screenshot started (interval: {AUTO_SCREENSHOT_INTERVAL}s)")
        
    async def stop(self):
        """Stop the automatic screenshot background task."""
        if not self.is_running:
            return
            
        self.is_running = False
        if self.task:
            self.task.cancel()
            try:
                await self.task
            except asyncio.CancelledError:
                pass
            self.task = None
            
        logger.info("🛑 Auto-screenshot stopped")
        
    async def _background_loop(self):
        """Main background loop for taking automatic screenshots."""
        logger.info("Auto-screenshot background loop started")
        
        while self.is_running:
            try:
                # Only take screenshots when Luna is active and not responding
                if self.is_active():
                    await self._take_background_screenshot()
                else:
                    if self.luna_is_responding:
                        logger.debug("Auto-screenshot: Luna is responding, skipping...")
                    elif len(self.active_channels) == 0:
                        logger.debug("Auto-screenshot: No active channels, skipping...")
                    else:
                        logger.debug("Auto-screenshot: Inactive, skipping...")
                    
                # Wait for the next interval
                await asyncio.sleep(AUTO_SCREENSHOT_INTERVAL)
                
            except asyncio.CancelledError:
                logger.info("Auto-screenshot loop cancelled")
                break
            except Exception as e:
                logger.error(f"Error in auto-screenshot loop: {e}", exc_info=True)
                # Continue running even if there's an error
                await asyncio.sleep(AUTO_SCREENSHOT_INTERVAL)
                
    async def _take_background_screenshot(self):
        """Take a background screenshot and analyze it."""
        try:
            # Use a simple context prompt for background awareness with user context
            context_prompt = "I'm looking at Gavin's screen right now. What application or activity is currently visible on his screen? Describe briefly in 1-2 sentences what he's doing."
            
            result = await optimized_screenshot_manager.capture_and_analyze_fast(context_prompt)
            
            if not result["success"]:
                logger.warning(f"Auto-screenshot failed: {result.get('error', 'Unknown error')}")
                return
                
            analysis = result["analysis"]
            total_time = result.get("total_time", 0)
            
            # Check if content is significantly different from last screenshot
            if self._is_content_similar(analysis):
                self.consecutive_similar_count += 1
                # Skip storing if we've seen similar content multiple times
                if self.consecutive_similar_count > 3:
                    logger.debug("Auto-screenshot: Skipping similar content")
                    return
            else:
                self.consecutive_similar_count = 0
                
            self.last_screenshot_content = analysis
            
            # Create background observation entry
            observation = {
                "timestamp": time.time(),
                "content": analysis,
                "type": "background_screenshot",
                "processing_time": total_time
            }
            
            # Store in memory (limited by maxlen)
            self.background_observations.append(observation)
            
            # Log to database as background context (not as assistant response)
            if self.bot and hasattr(self.bot, 'user') and self.bot.user:
                log_message_to_db(
                    user_id=self.bot.user.id,
                    role="system",  # Use "system" role for background observations
                    content=f"[Background] {analysis}",
                    timestamp=time.time(),
                    channel_type="background",
                    channel_id=None
                )
            
            logger.info(f"📸 Auto-screenshot: {analysis[:50]}... ({total_time:.2f}s)")
            
        except Exception as e:
            logger.error(f"Error taking background screenshot: {e}", exc_info=True)
            
    def _is_content_similar(self, new_content: str) -> bool:
        """Check if new content is similar to the last screenshot."""
        if not self.last_screenshot_content:
            return False
            
        # Simple similarity check - could be made more sophisticated
        old_words = set(self.last_screenshot_content.lower().split())
        new_words = set(new_content.lower().split())
        
        if len(old_words) == 0 and len(new_words) == 0:
            return True
            
        # Calculate Jaccard similarity
        intersection = len(old_words & new_words)
        union = len(old_words | new_words)
        
        if union == 0:
            return True
            
        similarity = intersection / union
        return similarity > 0.7  # 70% similarity threshold
        
    def get_recent_observations(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get recent background observations for context."""
        return list(self.background_observations)[-limit:]
        
    def get_observations_since(self, timestamp: float) -> List[Dict[str, Any]]:
        """Get background observations since a specific timestamp."""
        return [obs for obs in self.background_observations if obs["timestamp"] > timestamp]
        
    def clear_observations(self):
        """Clear all stored background observations."""
        self.background_observations.clear()
        logger.info("Auto-screenshot: Cleared all background observations")

# Global instance
auto_screenshot_manager = AutoScreenshotManager() 