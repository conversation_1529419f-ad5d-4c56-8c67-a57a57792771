"""
LLM Response Package
====================

This package handles the logic for processing user messages, interacting with
language models (LM Studio), managing conversation state,
performing RAG, and generating appropriate responses for the Discord bot.
"""

import logging

# Configure logging for the package
# Basic configuration, can be customized further in the main application
# logging.getLogger(__name__).addHandler(logging.NullHandler())

# Expose key functions and classes for easier import
from .config import (
    LM_STUDIO_MODEL_NAME,
    DECISION_MODEL,
    # USER_ALIASES, # Removed, replaced by USER_PROFILES and helpers
    TRANSCRIPT_LOG_CHANNEL_ID,
    CALL_NOTIFICATION_CHANNEL_ID_STR,
    PROMPT_LOG_CHANNEL_ID,
    DB_PATH
)
from .db_logger import log_message_to_db, get_conversation_history
from .initialization import (
    initialize_lm_studio,
    get_lm_studio_client,
    initialize_ollama,  # Legacy function - now calls llama.cpp
    get_ollama_client,  # Legacy function - now returns llama.cpp client
    initialize_llama_cpp,
    get_llama_cpp_client,
    create_lm_studio_client,
    create_kokoro_client
)
from .decision import should_respond
from .rag_utils import rewrite_query_for_rag, format_conversation_history_for_prompt
from .commands import (
    handle_screenshot_command,
    generate_call_message,
    analyze_image_url_optimized,
    process_image_mention
)
from .processing import process_user_message

# You can define __all__ to specify what `from llm_response import *` imports
__all__ = [
    # Config constants (optional, maybe better to import directly)
    "LM_STUDIO_MODEL_NAME", "DECISION_MODEL",
    "TRANSCRIPT_LOG_CHANNEL_ID", "CALL_NOTIFICATION_CHANNEL_ID_STR",
    "PROMPT_LOG_CHANNEL_ID", "DB_PATH",

    # Core functions
    "initialize_lm_studio",
    "get_lm_studio_client",
    "initialize_ollama",  # Legacy - now calls llama.cpp
    "get_ollama_client",  # Legacy - now returns llama.cpp client
    "initialize_llama_cpp",
    "get_llama_cpp_client",
    "log_message_to_db",
    "get_conversation_history",
    "should_respond",
    "process_user_message",
    "process_image_mention", # Note potential circular dependency if called from process_user_message

    # Helper/Utility functions (expose if needed externally)
    "create_lm_studio_client",
    "create_kokoro_client",
    "rewrite_query_for_rag",
    "format_conversation_history_for_prompt",
    "handle_screenshot_command",
    "generate_call_message",
    "analyze_image_url_optimized",
]

logger = logging.getLogger(__name__)
logger.info("llm_response package initialized.")