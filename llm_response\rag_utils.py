import logging
import time

# Import necessary components from other modules within the package
from .config import R<PERSON><PERSON><PERSON>E_MODEL, REWRITE_TEMP, REWRITE_MAX_TOKENS
from .initialization import create_lm_studio_client
# Removed safe_model_call import - using direct client calls instead

logger = logging.getLogger(__name__)

async def rewrite_query_for_rag(original_query: str) -> str:
    """Rewrites a user query into a better format for RAG retrieval using an LLM."""
    if not original_query:
        return ""

    # Use LM Studio client for rewriting
    client = create_lm_studio_client()
    model_name = REWRITE_MODEL # Use model from config

    prompt = f"""Rewrite the following user query into a concise keyword-based search query suitable for information retrieval. Focus on the core nouns, verbs, and entities. Remove conversational filler.

    Original Query: "{original_query}"

    Rewritten Search Query:"""

    messages = [
        {"role": "system", "content": "You are an AI assistant that rewrites user queries into effective search terms."},
        {"role": "user", "content": prompt}
    ]

    try:
        rewrite_start_time = time.monotonic()
        response = await client.chat.completions.create(
            model=model_name,
            messages=messages,
            temperature=REWRITE_TEMP, # Use temp from config
            max_tokens=REWRITE_MAX_TOKENS # Use max_tokens from config
        )
        rewrite_end_time = time.monotonic()
        logger.info(f"LM STUDIO QUERY REWRITE API CALL ({model_name}): Completed in {rewrite_end_time - rewrite_start_time:.6f}s")

        # Check for errors from the API call
        if not response or not response.choices or response.choices[0].finish_reason == "error":
            logger.error(f"Query rewrite LLM call failed or returned error structure for query: '{original_query}'. Using original.")
            return original_query

        rewritten_query = response.choices[0].message.content.strip().replace('"', '')

        # Basic validation: ensure it's not empty and significantly different
        if rewritten_query and rewritten_query.lower() != original_query.lower():
            logger.info(f"Rewrote query for RAG: '{original_query}' -> '{rewritten_query}'")
            return rewritten_query
        else:
            logger.warning(f"Query rewrite resulted in empty or identical phrase. Using original: '{original_query}'")
            return original_query
    except Exception as e:
        logger.error(f"Error rewriting query '{original_query}': {e}", exc_info=True)
        return original_query # Fallback to original query on error


def format_conversation_history_for_prompt(history: list, max_entries=10) -> str:
    """Formats the last few history entries for inclusion in a prompt."""
    if not history:
        return "No recent history available."

    formatted_lines = []
    # Take the last 'max_entries' from the history
    recent_history = history[-max_entries:]
    for msg in recent_history:
        role = msg.get("role", "unknown")
        content = msg.get("content", "")
        user_id = msg.get("user_id", "") # Keep user_id for potential context
        # Basic formatting, could add timestamps or user names if needed later
        # Use a more descriptive role label
        role_label = "Luna" if role == "assistant" else f"User ({user_id})"
        formatted_lines.append(f"{role_label}: {content}")

    return "\n".join(formatted_lines)