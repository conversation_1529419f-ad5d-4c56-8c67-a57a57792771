import threading
from typing import Dict, Any

try:
    from llama_cpp import Llama
except ImportError:  # Fallback if llama_cpp is not yet installed when this file is imported
    Llama = Any  # type: ignore

# ---------------------------------------------------------------------------
#  Singleton model registry
# ---------------------------------------------------------------------------

# Map arbitrary keys (e.g. "main", "decision", path) -> Llama instance
_models: Dict[str, Llama] = {}
_models_lock = threading.Lock()  # Guards model construction

# A *single* global lock that protects calls into llama.cpp.
# llama.cpp is **not** re-entrant or thread-safe. If two Python threads call
# `.generate()` / `__call__()` concurrently on the *same* or on *different*
# Llama instances that share the GPU context, it will crash with Windows
# error 0xe06d7363. Serialising all calls fixes that while keeping latency
# low compared with constantly re-creating model contexts.
_inference_lock = threading.Lock()

# ---------------------------------------------------------------------------
#  Public helpers
# ---------------------------------------------------------------------------

def get_or_create_model(key: str, *, model_path: str, **llama_kwargs) -> Llama:
    """Return an existing Llama model or construct it once in a threadsafe way.

    Parameters
    ----------
    key
        Arbitrary identifier used as the lookup key (e.g. "main", "decision").
    model_path
        Path to the GGUF/GGML model file.
    **llama_kwargs
        Additional keyword arguments forwarded to `llama_cpp.Llama`.
    """
    with _models_lock:
        model = _models.get(key)
        if model is None:
            model = Llama(model_path=model_path, **llama_kwargs)
            _models[key] = model
        return model


def call_model_safe(model: "Llama", prompt: str, **kwargs):
    """Thread-safe wrapper around `model()` for *non-streaming* inference."""
    with _inference_lock:
        # Ensure stream is disabled unless the caller explicitly overrides it.
        kwargs.setdefault("stream", False)
        return model(prompt, **kwargs)


def stream_model_safe(model: "Llama", prompt: str, **kwargs):
    """Thread-safe wrapper for *streaming* inference.

    This acquires the global inference lock for the entire duration of the
    generation to ensure no other thread enters llama.cpp until the stream
    finishes. This is required because llama.cpp internally keeps global
    buffers that are not protected by a mutex.
    """
    with _inference_lock:
        kwargs["stream"] = True  # Hard-enforce streaming mode
        for chunk in model(prompt, **kwargs):
            yield chunk 